<?php
$block = array_merge([
    'id' => '',
    'className' => '',
    'anchor' => '',
], $block);

global $content;
$content            = get_field('block_content');
$image              = get_field('block_image');
$backgroundGroup    = get_field('block_background');

get_template_part('ad-banner/parts/preview');

if (!empty($content)) : ?>
    <?php
    $blockClass = getClass([
        'ad-banner-block-main ad-banner-block-main--1080-1920 background js-ad-banner-export',
        'ad-banner-block-main--' . $block['id'],
        $block['className'],
    ]);

    addBackgroundInlineStyle('.ad-banner-block-main--' . $block['id'], $backgroundGroup);
    ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $blockClass; ?>">

        <?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
        <?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

        <?php
        $contentContainerClass = getClass([
            'ad-banner-block-main__content',
            [
                'condition' => !empty($content['invert_text_color']),
                'name'      => 'ad-banner-block-main__content--invert-font-color',
                'else-name' => 'ad-banner-block-main__content--normal-font-color',
            ],
        ]);
        ?>

        <div class="<?= $contentContainerClass; ?>">
            <div class="ad-banner-block-main__logo"><?= vector('logo', 'logos'); ?></div>

            <?php if (!empty($image)) : ?>
                <div class="ad-banner-block-main__image">
                    <?= get_template_part('campaigns/parts/offer-media', null, $image); ?>
                </div>
            <?php endif; ?>

            <?php
            $titleclass = getClass([
                'ad-banner-block-main__title',
                [
                    'condition' => !empty($content['hide_title_mobile']),
                    'name' => 'ad-banner-block-main__title--hide-mobile',
                ],
            ]);
            ?>
            <h1 class="<?= $titleclass; ?>"><?= $content['campaign_header']; ?></h1>
            <?php if (!empty($content['campaign_subtitle'])) : ?>
                <h2 class="ad-banner-block-main__subtitle"><?= $content['campaign_subtitle']; ?></h2>
            <?php endif; ?>
            <?php if (!empty($content['campaign_description'])) : ?>
                <div class="ad-banner-block-main__description">
                    <?php get_template_part('components/read-more', null, $content['campaign_description']); ?>
                </div>
            <?php endif; ?>

            <?php if (get_field('show_countdown')) :
                $countdownText = get_field('countdown_text');
                if (!empty($countdownText)) : ?>
                    <div class="ad-banner-block-main__countdown-text" data-html2canvas-ignore="true"><?= $countdownText; ?></div>
                <?php endif; ?>

                <div class="ad-banner-block-main__countdown-time" data-html2canvas-ignore="true">
                    <?php get_template_part('components/countdown', null, [
                        'status' => 100, // a none existing status message
                        'time_end' => getDateTime(get_field('countdown_to')),
                        'type' => 'time',
                        'time_format' => '00:00:00:00',
                    ]); ?>
                </div>
            <?php endif; ?>
        </div>
    </section>
<?php endif;
