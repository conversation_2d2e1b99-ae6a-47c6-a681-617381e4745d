<?php
class Player
{
    private static $instance;
    private $debug    = false;
    private $loggedIn = false;
    private $data     = [
        'id'              => 0,
        'username'        => '',
        'email'           => '',
        'mobile'          => '',
        'firstName'       => '',
        'lastName'        => '',
        'countryId'       => 0,
        'franchiseId'     => 0,
        'playerSegmentVO' => [ // api old segment values
            'valueSegmentCurrent'          => '',
            'currentAvgDeptAmount'         => '',
            'valueSegmentHighest'          => '',
            'allBrandsValueSegmentHighest' => '',
        ],
        'playerSegmentation' => [ // api new segment values
            'currentValueSegmentName' => '',
            'highestValueSegmentName' => '',
        ]

        /**
         * Segments values NEW -> Old
         * Check getSegmentValue() method for the logic
         *
         * Inactive -> Low Value
         * Normal LOW -> Low Value
         * Normal MEDIUM -> Medium Value
         * Normal HIGH -> High Value
         * VIP LOW -> VIP
         * VIP NORMAL -> Winners
         * VIP DIAMOND -> Diamond (new acf tab added for the new api)
         */
    ];
    public $sessionInfo = [];

    public static function getInstance()
    {
        if (!isset(self::$instance)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    public function getInfoFromApi()
    {
        if (!empty($_COOKIE['sessionId'])) {
            $api = new Api(brandUrl() . '/player/account');
            $response = $api->cookie('sessionId=' . $_COOKIE['sessionId']);

            if ( !empty($response['status']) && $response['status'] === 'SUCCESS' ) {
                $this->data = $response['result'];

                // Create loggedInTime cookie if API response contains loggedInTime value
                if(!empty($response['result']['loggedInTime'])) {
                    $expiresTimestamp = (int) $response['result']['loggedInTime'];

                    // Convert milliseconds to seconds not to have 'expires' can not have year beyond 9999
                    if (strlen((string) $response['result']['loggedInTime']) > 10) {
                        $expiresTimestamp = (int) ($response['result']['loggedInTime'] / 1000);
                    }

                    setcookie('loggedInTime',  (int) $response['result']['loggedInTime'], ['expires' => $expiresTimestamp + ********]);
                }
            } else {
                do_action('qm/debug', $response);
            }
        } else {
            // If we're not in simulated user mode, delete loggedInTime cookie since we're not logged in
            if(empty(SIMULATED_USER)) {
                // Delete loggedInTime cookie, because sessionId cookie not found
                if(!empty($_COOKIE['loggedInTime'])) {
                    if(DEBUG_MODE) {
                        do_action('qm/debug', 'sessionId cookie not found, therefore loggedInTime cookie deleted');
                    }

                    setcookie('loggedInTime', '', time() - 3600);
                    unset($_COOKIE['loggedInTime']);
                }
            }
        }
    }

    public function getCurrentSessionInfo()
    {
        if (!empty($_COOKIE['sessionId'])) {
            $api = new Api(brandUrl() . '/player/currentSessionInfo');
            $response = $api->cookie('sessionId=' . $_COOKIE['sessionId']);

            if ($response) {
                if(!empty($response['status']) && $response['status'] === 'SUCCESS') {
                    $this->sessionInfo = $response['result'];
                }
            }
        }
    }
    public function logoutFromApi()
    {
        if (!empty($_COOKIE['sessionId'])) {
            $api = new Api(brandUrl() . '/player/session/logout');
            $response = $api->cookie('sessionId=' . $_COOKIE['sessionId']);
        }
    }

    public function checkEligibilityFromApi($RTPG_id)
    {
        if (!empty($_COOKIE['sessionId'])) {
            $api = new Api(brandUrl() . '/player/groups/eligible/' . $RTPG_id);
            $response = $api->cookie('sessionId=' . $_COOKIE['sessionId']);

            return (!empty($response['result']['isPlayerInGroup']) && $response['result']['isPlayerInGroup'] == true);
        }
    }

    public function simulatedUser()
    {
        global $current_user;
        wp_get_current_user();

        // Simulate API data for variable
        // create dummy login credentials that mimics real one in the real version we will actually call the player API

        $this->debug    = true;
        $this->loggedIn = true;

        // Get selected segment from transient, default to 'normal low' if not set
        $selectedSegment = get_transient('px_simulated_segment_' . $current_user->ID) ?: 'normal low';
        $selectedSegment = strtoupper($selectedSegment);

        $this->data     = [
            'id'                 => '0',
            'username'           => $current_user->user_login,
            'email'              => $current_user->user_email,
            'mobile'             => '**********',
            'firstName'          => $current_user->user_firstname,
            'lastName'           => $current_user->user_lastname,
            'countryId'          => 1,
            'franchiseId'        => 1,
            'playerSegmentation' => [ // api new segment values
                'currentValueSegmentName' => $selectedSegment,
                'highestValueSegmentName' => $selectedSegment,
            ],
            /* 'playerSegmentVO' => [ // api old segment values
                'valueSegmentCurrent' => 'LV',
                'currentAvgDeptAmount' => 'TOP',
                'valueSegmentHighest' => 'LV',
                'allBrandsValueSegmentHighest' => 'LV',
            ], */
        ];

        $this->sessionInfo = [
            "initialBalance" => "€123",
            "totalStake" => "€10",
            "totalWinOrLoss" => "€-5"
        ];

        // Create loggedInTime cookie
        if (empty($_COOKIE['loggedInTime'])) {
            setcookie('loggedInTime', time(), ['expires' => time() + ********]);
        }
    }

    // Check if user is in correct RTPG group
    public function checkEligibility($RTPG_id)
    {
        // if the RTPG id is null, it means we want everyone to participate
        if (!$RTPG_id || $RTPG_id === NULL) {
            return true;
        }

        // If the player is not logged in then he's not qualified
        if (!$this->isLoggedIn()) {
            return false;
        }

        if ($this->debug && $RTPG_id == **********) {
            return true;
        }

        return $this->checkEligibilityFromApi($RTPG_id);
    }

    public function isLoggedIn()
    {
        /*
            If sessionId cookie is set, it means player may be loggedin, or cookie left-over.
            Therefore, we gotta confirm if session is still alive via player API
        */
        if ( !empty($_COOKIE['sessionId']) && !($this->loggedIn) ) {
            $api = new Api(brandUrl() . '/player/account');
            $response = $api->cookie('sessionId=' . $_COOKIE['sessionId']);

            if ($response) {
                $this->loggedIn = (!empty($response['status']) && $response['status'] === 'SUCCESS');
            }
        }
        return $this->loggedIn;
    }

    public function getId()
    {
        return $this->data['id'];
    }

    public function getFirstname()
    {
        return $this->data['firstName'];
    }

    public function getLastname()
    {
        return $this->data['lastName'];
    }

    public function getUsername()
    {
        return $this->data['username'];
    }

    public function  getMaskedFullName()
    {
        return substr((string) $this->data['firstName'], 0, 1) . '***' . ' ' . substr((string) $this->data['lastName'], 0, 1) . '.';
    }

    public function getEmail()
    {
        return $this->data['email'];
    }

    public function getPhone()
    {
        return $this->data['mobile'];
    }

    public function getFranchiseId()
    {
        return $this->data['franchiseId'];
    }

    public function getCountryId()
    {
        return $this->data['countryId'];
    }

    public function getSegmentValue($segmentType = 'current')
    {
        if (!$this->isLoggedIn()) {
            return getLowestPlayerSegmentValue();
        }
        $segmentValue = match ($segmentType) {
            'highest' => strtolower((string) $this->getValueSegmentHighest()),
            default => strtolower((string) $this->getValueSegmentCurrent()),
        };

        if (array_key_exists($segmentValue, PLAYER_SEGMENTS)) {
            return PLAYER_SEGMENTS[$segmentValue];
        }

        return $segmentValue;
    }

    public function getValueSegmentCurrent()
    {
        if (!empty($this->data['playerSegmentation']['currentValueSegmentName'])) { // get new api value first
            return $this->data['playerSegmentation']['currentValueSegmentName'];
        }

        if (!empty($this->data['playerSegmentVO']['valueSegmentCurrent'])) { // get old api value if the new value not found
            return $this->data['playerSegmentVO']['valueSegmentCurrent'];
        }

        return '';
    }

    public function getValueSegmentHighest()
    {
        if (!empty($this->data['playerSegmentation']['highestValueSegmentName'])) { // get new api value first
            return $this->data['playerSegmentation']['highestValueSegmentName'];
        }

        if (!empty($this->data['playerSegmentVO']['valueSegmentHighest'])) { // get old api value if the new value not found
            return $this->data['playerSegmentVO']['valueSegmentHighest'];
        }

        return '';
    }

    public function getAllBrandsValueSegmentHighest() // old api had this value only
    {
        if (!empty($this->data['playerSegmentVO']['allBrandsValueSegmentHighest'])) {
            return $this->data['playerSegmentVO']['allBrandsValueSegmentHighest'];
        }

        return '';
    }

    public function getCurrentAvgDeptAmount() // old api had this value only
    {
        if (!empty($this->data['playerSegmentVO']['currentAvgDeptAmount'])) {
            return $this->data['playerSegmentVO']['currentAvgDeptAmount'];
        }

        return '';
    }
}
