<?php

GeoIPBlocker::on_load();

class GeoIPBlocker
{
    private $url;
    private $endpoint;
    private $geoipShortUrl;
    private $cacheTime;
    private $blockedCountries;
    private $userCountry;
    private $whitelistedIPAddresses;

    static function on_load()
    {
        add_action('init', [self::class, 'init']);
    }

    static function init()
    {
        new GeoIPBlocker;
    }

    function __construct()
    {
        global $wpdb;

        if (! function_exists('acf_get_setting')) {
            add_action('admin_notices', function () {
                $class          = 'notice notice-error';
                $notice_message = 'GeoIPBlocker could not be loaded. Because acf_get_setting function does not exist. If you are sure ACF plugin is enabled, than an ACF update might caused this.';
                printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), esc_html($notice_message));
            }
            );
            return;
        }


        // Multi-lang (WPML Support) for acf field
        $defaultLang = acf_get_setting('default_language');
        $currentLang = acf_get_setting('current_language');
        $langPrefix = ($currentLang !== $defaultLang) ? $currentLang . '_' : '';

        $geoip_blocker_enabled = $wpdb->get_var("SELECT option_value FROM {$wpdb->options} WHERE option_name = 'options_" . $langPrefix . "geoip_blocker'");

        // If GeoIP Blocker toggle under Brand Settings is enabled, get the countries to be blocked and check visitor's country
        if ($geoip_blocker_enabled && !empty($_SERVER['HTTP_USER_AGENT'])) {
            do_action('qm/debug', 'GeoIP ENABLED');
            $this->url = 'https://api.co-gaming.com/v2/keys';
            $this->endpoint = '/SYSTEM/BLOCK_ACCESS/COUNTRIES_TO_BLOCK/';
            $this->geoipShortUrl = 'http://geoip:8080/json/'; // TODO: Make use of https://www.sunmaker.de/geoip
            $this->cacheTime = 30 * 24 * 60 * 60; // Data will be cached for a month
            $this->blockedCountries = $this->getCountries();
            $this->userCountry = getCountryCode();
            $this->whitelistedIPAddresses = [
                '*************', // VPN - London
                '**************', // VPN - Malta 1
                '*************', // VPN - Malta 2
                '*************', // VPN - Gib
                '*************', // VPN - Nagios MT
                '************', // VPN - Norway
                '***********', // VPN - Umea
                '***********', // VPN - Stockholm
                '***********', // VPN - SSLVPN
                '**************', // Test automation IP from Google Kubernetes container
                '**************', // sth-test-phoenix01
                '*************', // Cloudways
                '**************',
                '***************',
                '***************',
                '*************',
                '*************',
                '**************',
                '*************',
                '************',
                '*************',
                '**************',
                '**************',
                '*************',
                '*************',
                '***************',
                '**************',
                '*************',
                '**************',
                '**************',
                '**********',
                '************',
                '*************',
                '************',
                '************',
                '*************',
                '***********',
                '************',
                '************',
                '*************',
                '**************'
            ];

            // If visitor IP is not in the whitelist
            if(!in_array(getIP(), $this->whitelistedIPAddresses)) {
                // Checking the count of blockedCountries array (transient: px_api_blocked_countries) to see if there is any blocked country set
                if (!is_user_logged_in() && !is_login() && (count($this->blockedCountries))) {
                    if (!empty($this->userCountry)) {
                        if (in_array($this->userCountry, $this->blockedCountries)) {
                            wp_redirect(brandUrl() . '/accessblocked/');
                            exit();
                        }
                    }
                }
            }
        }
    }

    private function getCountries()
    {
        $response = get_transient('px_api_blocked_countries');

        // If cached data is empty or not found fetch from GeoIP API and cache it
        if (!($response) || empty($response) || $response === NULL) {
            $api = new Api($this->url . $this->endpoint);
            $response = $api->get();

            if (!empty($response['node'])) {
                $countries = [];
                $response = $response['node']['nodes'];

                foreach ($response as $i => $val) {
                    $brandName = strtolower(str_replace($this->endpoint, '', (string) $response[$i]['key']));

                    // Folkeriket workaround
                    if ($brandName == 'folkeautomaten') {
                        $brandName = 'folkeriket';
                    }

                    if ($brandName == CURRENT_BRAND) {
                        $countries = str_replace(' ', '', (string) $response[$i]['value']);
                    }
                }

                set_transient('px_api_blocked_countries', $countries, $this->cacheTime);
                $response = $countries;
            }
        }

        do_action('qm/info', 'GeoIPBlocker::getCountries() -> ' . $response);

        return is_string($response) ? explode(',', $response) : [];
    }
}