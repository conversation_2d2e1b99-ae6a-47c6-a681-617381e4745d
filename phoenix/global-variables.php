<?php
define('RELEASE_VERSION', '2.9.16.18.1');

// Acquisition post type
define('ACQUISITION_SLUG', 'acquisition');
define('ACQUISITION_TEMPLATE_SLUG', 'acquisition_template');
define('ACQUISITION_TAG_SLUG', 'acquisition_tag');

// Dynamic post type
define('DYNAMIC_SLUG', 'dynamic');
define('DYNAMIC_TAG_SLUG', 'dynamic_tag');

// CRM post type
define('CAMPAIGN_SLUG', 'campaign');
define('CAMPAIGN_TEMPLATE_SLUG', 'template');
define('CAMPAIGN_TAG_SLUG', 'campaign_tag');

// Quiz post type
define('QUIZ_SLUG', 'quiz');

// Letter Game post type
define('LETTER_GAME_SLUG', 'letter-game');

// Games Lib post type
define('CASINO_GAMES_SLUG', 'casino-games');
define('CASINO_GAMES_PROVIDER_SLUG', 'casino-games-provider');
define('CASINO_GAMES_GATEGORY_SLUG', 'casino-games-category');

// Start Page post type
define('START_PAGE_SLUG', 'start-page');
define('START_PAGE_TEMPLATE_SLUG', 'start_page_template');
define('START_PAGE_DYNAMIC_BLOCK_SLUG', 'dynamic-start-page-block');
define('START_PAGE_DYNAMIC_PNP_BLOCK_SLUG', 'dynamic-start-page-pnp-block');
define('START_PAGE_DYNAMIC_BLOCK_CATEGORY_SLUG', 'start_page_dynamic_block');
define('START_PAGE_DYNAMIC_PNP_BLOCK_CATEGORY_SLUG', 'start_page_dynamic_pnp_block');

// AD Banner
define('AD_BANNER_SLUG', 'ad-banner');
define('AD_BANNER_BLOCK_SLUG', 'ad-banner-block');
define('AD_BANNER_BLOCK_CATEGORY_SLUG', 'ad-banner');
// SEO Pages
define('SEO_PAGE_SLUG', 'seo-page');

// Help Page post type
define('HELP_PAGE_SLUG', 'help-page');
define('HELP_TOPIC_SLUG', 'help-topic');

// Responsible Gaming
define('RESPONSIBLE_GAMING_SLUG', 'responsible-gaming');
define('RESPONSIBLE_GAMING_BLOCK_SLUG', 'responsible-gaming-block');
define('RESPONSIBLE_GAMING_BLOCK_CATEGORY_SLUG', 'responsible-gaming');

// Player Segment values in desc order. we can use it as fallback, to get the highest offer for the player segment value
define('PLAYER_SEGMENTS', [
        'vip diamond'   => 'diamond',
        'vip normal'    => 'winners',
        'vip low'       => 'vip',
        'normal high'   => 'hv',
        'normal medium' => 'mv',
        'normal low'    => 'lv',
        'inactive'      => 'lv',
]);

// Jackpots providers
define('JACKPOT_PROVIDE_REDTIGER', 'redtiger');
define('JACKPOT_PROVIDE_PLAYNGO', 'playngo');
define('JACKPOT_PROVIDE_EVOLUTION', 'evolution');
define('JACKPOT_PROVIDE_NETENT', 'netent');

define('JACKPOT_PROVIDERS', [
        JACKPOT_PROVIDE_REDTIGER => [
            'name' => 'RedTiger',
            'jackpots' => [
                'super'         => 'Super Drop',
                'daily'         => 'Daily Drop',
                'hourly'        => 'Hourly Drop',
                '10mins'        => '10 Minutes Drop'
            ]
        ],
        JACKPOT_PROVIDE_PLAYNGO => [
            'name' => 'PlaynGo',
            'jackpots' => [
                'mega'      => 'Mega',
                'grand'     => 'Grand',
                'major'     => 'Major',
                'minor'     => 'Minor',
                'mini'      => 'Mini'
            ]
        ],
        JACKPOT_PROVIDE_EVOLUTION => [
            'name' => 'Evolution',
            'jackpots' => [
                'casino_holdem_jumbo7'      => 'Casino Holdem Jumbo7 Jackpot',
                'caribbean_stud_poker'      => 'Caribbean Stud Poker Jackpot',
                'texas_holdem_bonus_poker'  => 'Texas Holdem Bonus Poker Jackpot'
            ]
        ], JACKPOT_PROVIDE_NETENT => [
            'name' => 'NetEnt',
            'jackpots' => [
                'megajackpot1'              => 'Mega Fortune 1',
                'megajackpot2'              => 'Mega Fortune 2',
                'megajackpot3'              => 'Mega Fortune 3',
                'megafortunedreams_rapid'   => 'Mega Fortune Dreams Rapid',
                'megafortunedreams_mega'    => 'Mega Fortune Dreams Mega',
                'megafortunedreams_major'   => 'Mega Fortune Dreams Major',
                'hog_small'                 => 'Hall of Gods Small',
                'hog_medium'                => 'Hall of Gods Medium',
                'hog_large'                 => 'Hall of Gods Large',
                'gof_mega'                  => 'Divine Fortune',
                'bof_mega'                  => 'Mercy Of The Gods',
                'tt_mega'                   => 'Grand Spinn Superpot',
                'megajoker'                 => 'Mega Joker',
                'vegasnightlife_mega'       => 'Vegas Night Life Mega',
                'vegasnightlife_r3_mega'    => 'Vegas Night Life R3 Mega',
                'arabian'                   => 'Arabian Nights',
                'imperialriches_mega'       => 'Imperial Riches Mega',
                'imperialriches_major'      => 'Imperial Riches Major',
                'imperialriches_midi'       => 'Imperial Riches Midi',
                'imperialriches_minifix'    => 'Imperial Riches Minifix',
                'imperialriches_rapidfix'   => 'Imperial Riches Rapidfix',
                'porkknox_mega'             => 'Pork Knox',
                'happypanda_mega'           => 'Happy Panda',
                'goldmoneyfrog1'            => 'Gold Money Frog 1',
                'goldmoneyfrog2'            => 'Gold Money Frog 2',
                'goldmoneyfrog3'            => 'Gold Money Frog 3'
            ]
        ]
]);

define('JACKPOT_IDS_REDTIGER', [
        44001 => 'daily',
        44002 => 'super',
        44003 => 'hourly',
        44004 => '10mins'
]);

define('JACKPOT_IDS_EVOLUTION', [
        'lro5gnm5cd4qac5x' => 'casino_holdem_jumbo7',
        'lgeqrzs5bkkaalua' => 'caribbean_stud_poker',
        'mbe53sxodctqaals' => 'texas_holdem_bonus_poker'
]);

/* Global variables */

// Whitelisted tracking parameters by Omarsys
define('TRACKING_PARAMS', [
    'aff',
    'fp',
    'fmb',
    'transaction_id',
    'cid',
    'clickid',
    'var',
    'mid',
    'sid',
    'aid',
    'pid',
    'gclsrc',
    'utm_source',
    'utm_medium',
    'utm_campaign',
    'utm_term',
    'utm_content'
]);

// Debug, Simulate Login and Date Time
global $current_user;
wp_get_current_user();
define('DEBUG_MODE', (!empty(get_transient('px_debug_mode_' . $current_user->ID)) && get_transient('px_debug_mode_' . $current_user->ID)));
define('SIMULATED_USER', (!empty(get_transient('px_simulated_user_' . $current_user->ID)) && get_transient('px_simulated_user_' . $current_user->ID)));
define('SIMULATED_TIME', !empty(get_transient('px_time_value_' . $current_user->ID)) ? get_transient('px_time_value_' . $current_user->ID) : false);

// Site type envs
define('SITE_TYPE_PROMO', 'promo');
define('SITE_TYPE_RG', 'rg');
define('SITE_TYPE_GO', 'go');
define('SITE_TYPE_FAQ', 'faq');
define('SITE_TYPE_SEO', 'seo');
define('SITE_TYPE_BLOG', 'blog');
define('SITE_TYPE_DEV', 'dev');
define('SITE_TYPE_LOCAL', 'local');
define('SITE_TYPE_STAGING', 'staging');
define('SITE_TYPE_PRODUCTION', 'production');
define('SITE_TYPES', [SITE_TYPE_PROMO, SITE_TYPE_RG, SITE_TYPE_GO, SITE_TYPE_FAQ, SITE_TYPE_SEO, SITE_TYPE_BLOG]);
define('SIMULATED_SITE_TYPE', (!empty(get_transient('px_simulated_site_type')) && get_transient('px_simulated_site_type')) ? get_transient('px_simulated_site_type') : false);

// Define instance
$instance = !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'prod'; // fallback for non-browser visits ie. $_SERVER['SERVER_NAME'] undefined

// Development Environment
define('DEV_ENV', (preg_match('/phnxdev/', $instance)));

// Local Development Environment
define('LOCAL_ENV', (preg_match('/localhost/', $instance)));

// Staging Development Enviroment
define('STAGING_ENV', (preg_match('/phnxstaging|phnxstg|phoenix|cleverdolphin|.one/', $instance)));

// Proxy Environment
define('PROXY_ENV', (preg_match('/' . SITE_TYPE_GO . '|' . SITE_TYPE_RG . '|' . SITE_TYPE_FAQ . '/', $instance)));

// Site Type Global, works only with PHP 8+
$site_url = get_site_url();

// For dev, stg, local environments
if (SIMULATED_SITE_TYPE) define('SITE_TYPE', SIMULATED_SITE_TYPE);
elseif (DEV_ENV) define('SITE_TYPE', SITE_TYPE_PROMO);
elseif (STAGING_ENV) define('SITE_TYPE', SITE_TYPE_PROMO);
elseif (LOCAL_ENV) define('SITE_TYPE', SITE_TYPE_PROMO);
elseif (str_contains($site_url, '/go')) define('SITE_TYPE', SITE_TYPE_GO);
elseif (str_contains($site_url, '/rg')) define('SITE_TYPE', SITE_TYPE_RG);
elseif (str_contains($site_url, '/faq')) define('SITE_TYPE', SITE_TYPE_FAQ);
elseif (str_contains($site_url, 'comeonnews')) define('SITE_TYPE', SITE_TYPE_BLOG);
elseif (str_contains($site_url, 'blog.')) define('SITE_TYPE', SITE_TYPE_BLOG);
elseif (str_contains($site_url, 'promo.')) define('SITE_TYPE', SITE_TYPE_PROMO);
elseif (str_contains($site_url, 'promocje.')) define('SITE_TYPE', SITE_TYPE_PROMO);
elseif (str_contains($site_url, 'promotions.')) define('SITE_TYPE', SITE_TYPE_PROMO);
elseif (str_contains($site_url, 'kampanjer.')) define('SITE_TYPE', SITE_TYPE_PROMO);
elseif (str_contains($site_url, 'tarjoukset.')) define('SITE_TYPE', SITE_TYPE_PROMO);
else define('SITE_TYPE', SITE_TYPE_SEO); // default site type is SEO/Brand Protection

// End of Site Type Global

define('CURRENT_BRAND', getCurrentBrand());
define('CURRENT_REGION', getCurrentRegion());
define('CURRENT_AUTHORITY', getCurrentAuthority());
define('CURRENT_TIMEZONE', getCurrentTimezone());
define('CURRENT_LANGUAGE', getCurrentLanguage());
define('CURRENT_COUNTRY', getCurrentCountry());
define('CURRENT_CURRENCY', getCurrentCurrency());
define('CURRENT_CURRENCY_SYMBOL', getCurrentCurrencySymbol());
define('CURRENT_PATH', getCurrentPath());
define('CURRENT_DEVICE', getCurrentDevice());

if (DEBUG_MODE) {
    do_action('qm/info', 'SITE_TYPE: ' . SITE_TYPE);
    do_action('qm/info', 'CURRENT_BRAND: ' . CURRENT_BRAND);
    do_action('qm/info', 'CURRENT_REGION: ' . CURRENT_REGION);
    do_action('qm/info', 'CURRENT_TIMEZONE: ' . CURRENT_TIMEZONE);
    do_action('qm/info', 'CURRENT_LANGUAGE: ' . CURRENT_LANGUAGE);
    do_action('qm/info', 'CURRENT_AUTHORITY: ' . CURRENT_AUTHORITY);
    do_action('qm/info', 'CURRENT_COUNTRY: ' . CURRENT_COUNTRY);
    do_action('qm/info', 'CURRENT_CURRENCY: ' . CURRENT_CURRENCY);
    do_action('qm/info', 'CURRENT_CURRENCY_SYMBOL: ' . CURRENT_CURRENCY_SYMBOL);
    do_action('qm/info', 'CURRENT_PATH: ' . CURRENT_PATH);
    do_action('qm/info', 'CURRENT_DEVICE: ' . CURRENT_DEVICE);
    do_action('qm/info', 'SIMULATED_USER: ' . SIMULATED_USER);
}

define('COUNTDOWN_STARTS_IN', 'starts-in');
define('COUNTDOWN_NEW_OFFER_IN', 'new-offer-in');
define('COUNTDOWN_CAMPAIGN_ENDS_IN', 'campaign-ends-in');
define('COUNTDOWN_OFFER_ENDS_IN', 'offer-ends-in');
define('COUNTDOWN_RESUMES_IN', 'resumes-in');
define('COUNTDOWN_EXPIRED', 'expired');
define('COUNTDOWN_PREDICTION_OPEN', 'prediction-open');
define('COUNTDOWN_PREDICTION_CLOSED', 'prediction-closed');

// Zendesk
add_action('init', function () {
    global $ZENDESK_KEY, $ZENDESK_KEY_STAGING;
    $ZENDESK_KEY = (!get_field_tweaked('enable_zendesk', 'option') ? 0 : (DEV_ENV ? $ZENDESK_KEY_STAGING : $ZENDESK_KEY));
});

// Ada Support
add_action('init', function () {
    global $ada_support_widget;
    $ada_support_widget = get_field_tweaked('ada_support', 'option');
});

// Recaptcha
define('RECAPTCHA_SITE_KEY', '6Lc3BT4cAAAAAFm5SERP0D5ayDtTrnDUN9MGPn3O');
define('RECAPTCHA_SECRET_KEY', '6Lc3BT4cAAAAAPZEWiq-urJH5c2QbDO8au4eTrHK');

// Country names and codes, currently used for GeoIP blocker
define('COUNTRIES', [
    "BD" => "Bangladesh",
    "BE" => "Belgium",
    "BF" => "Burkina Faso",
    "BG" => "Bulgaria",
    "BA" => "Bosnia and Herzegovina",
    "BB" => "Barbados",
    "WF" => "Wallis and Futuna",
    "BL" => "Saint Barthelemy",
    "BM" => "Bermuda",
    "BN" => "Brunei",
    "BO" => "Bolivia",
    "BH" => "Bahrain",
    "BI" => "Burundi",
    "BJ" => "Benin",
    "BT" => "Bhutan",
    "JM" => "Jamaica",
    "BV" => "Bouvet Island",
    "BW" => "Botswana",
    "WS" => "Samoa",
    "BQ" => "Bonaire, Saint Eustatius and Saba ",
    "BR" => "Brazil",
    "BS" => "Bahamas",
    "JE" => "Jersey",
    "BY" => "Belarus",
    "BZ" => "Belize",
    "RU" => "Russia",
    "RW" => "Rwanda",
    "RS" => "Serbia",
    "TL" => "East Timor",
    "RE" => "Reunion",
    "TM" => "Turkmenistan",
    "TJ" => "Tajikistan",
    "RO" => "Romania",
    "TK" => "Tokelau",
    "GW" => "Guinea-Bissau",
    "GU" => "Guam",
    "GT" => "Guatemala",
    "GS" => "South Georgia and the South Sandwich Islands",
    "GR" => "Greece",
    "GQ" => "Equatorial Guinea",
    "GP" => "Guadeloupe",
    "JP" => "Japan",
    "GY" => "Guyana",
    "GG" => "Guernsey",
    "GF" => "French Guiana",
    "GE" => "Georgia",
    "GD" => "Grenada",
    "GB" => "United Kingdom",
    "GA" => "Gabon",
    "SV" => "El Salvador",
    "GN" => "Guinea",
    "GM" => "Gambia",
    "GL" => "Greenland",
    "GI" => "Gibraltar",
    "GH" => "Ghana",
    "OM" => "Oman",
    "TN" => "Tunisia",
    "JO" => "Jordan",
    "HR" => "Croatia",
    "HT" => "Haiti",
    "HU" => "Hungary",
    "HK" => "Hong Kong",
    "HN" => "Honduras",
    "HM" => "Heard Island and McDonald Islands",
    "VE" => "Venezuela",
    "PR" => "Puerto Rico",
    "PS" => "Palestinian Territory",
    "PW" => "Palau",
    "PT" => "Portugal",
    "SJ" => "Svalbard and Jan Mayen",
    "PY" => "Paraguay",
    "IQ" => "Iraq",
    "PA" => "Panama",
    "PF" => "French Polynesia",
    "PG" => "Papua New Guinea",
    "PE" => "Peru",
    "PK" => "Pakistan",
    "PH" => "Philippines",
    "PN" => "Pitcairn",
    "PL" => "Poland",
    "PM" => "Saint Pierre and Miquelon",
    "ZM" => "Zambia",
    "EH" => "Western Sahara",
    "EE" => "Estonia",
    "EG" => "Egypt",
    "ZA" => "South Africa",
    "EC" => "Ecuador",
    "IT" => "Italy",
    "VN" => "Vietnam",
    "SB" => "Solomon Islands",
    "ET" => "Ethiopia",
    "SO" => "Somalia",
    "ZW" => "Zimbabwe",
    "SA" => "Saudi Arabia",
    "ES" => "Spain",
    "ER" => "Eritrea",
    "ME" => "Montenegro",
    "MD" => "Moldova",
    "MG" => "Madagascar",
    "MF" => "Saint Martin",
    "MA" => "Morocco",
    "MC" => "Monaco",
    "UZ" => "Uzbekistan",
    "MM" => "Myanmar",
    "ML" => "Mali",
    "MO" => "Macao",
    "MN" => "Mongolia",
    "MH" => "Marshall Islands",
    "MK" => "Macedonia",
    "MU" => "Mauritius",
    "MT" => "Malta",
    "MW" => "Malawi",
    "MV" => "Maldives",
    "MQ" => "Martinique",
    "MP" => "Northern Mariana Islands",
    "MS" => "Montserrat",
    "MR" => "Mauritania",
    "IM" => "Isle of Man",
    "UG" => "Uganda",
    "TZ" => "Tanzania",
    "MY" => "Malaysia",
    "MX" => "Mexico",
    "IL" => "Israel",
    "FR" => "France",
    "IO" => "British Indian Ocean Territory",
    "SH" => "Saint Helena",
    "FI" => "Finland",
    "FJ" => "Fiji",
    "FK" => "Falkland Islands",
    "FM" => "Micronesia",
    "FO" => "Faroe Islands",
    "NI" => "Nicaragua",
    "NL" => "Netherlands",
    "NO" => "Norway",
    "NA" => "Namibia",
    "VU" => "Vanuatu",
    "NC" => "New Caledonia",
    "NE" => "Niger",
    "NF" => "Norfolk Island",
    "NG" => "Nigeria",
    "NZ" => "New Zealand",
    "NP" => "Nepal",
    "NR" => "Nauru",
    "NU" => "Niue",
    "CK" => "Cook Islands",
    "XK" => "Kosovo",
    "CI" => "Ivory Coast",
    "CH" => "Switzerland",
    "CO" => "Colombia",
    "CN" => "China",
    "CM" => "Cameroon",
    "CL" => "Chile",
    "CC" => "Cocos Islands",
    "CA" => "Canada",
    "CG" => "Republic of the Congo",
    "CF" => "Central African Republic",
    "CD" => "Democratic Republic of the Congo",
    "CZ" => "Czech Republic",
    "CY" => "Cyprus",
    "CX" => "Christmas Island",
    "CR" => "Costa Rica",
    "CW" => "Curacao",
    "CV" => "Cape Verde",
    "CU" => "Cuba",
    "SZ" => "Swaziland",
    "SY" => "Syria",
    "SX" => "Sint Maarten",
    "KG" => "Kyrgyzstan",
    "KE" => "Kenya",
    "SS" => "South Sudan",
    "SR" => "Suriname",
    "KI" => "Kiribati",
    "KH" => "Cambodia",
    "KN" => "Saint Kitts and Nevis",
    "KM" => "Comoros",
    "ST" => "Sao Tome and Principe",
    "SK" => "Slovakia",
    "KR" => "South Korea",
    "SI" => "Slovenia",
    "KP" => "North Korea",
    "KW" => "Kuwait",
    "SN" => "Senegal",
    "SM" => "San Marino",
    "SL" => "Sierra Leone",
    "SC" => "Seychelles",
    "KZ" => "Kazakhstan",
    "KY" => "Cayman Islands",
    "SG" => "Singapore",
    "SE" => "Sweden",
    "SD" => "Sudan",
    "DO" => "Dominican Republic",
    "DM" => "Dominica",
    "DJ" => "Djibouti",
    "DK" => "Denmark",
    "VG" => "British Virgin Islands",
    "DE" => "Germany",
    "YE" => "Yemen",
    "DZ" => "Algeria",
    "US" => "United States",
    "UY" => "Uruguay",
    "YT" => "Mayotte",
    "UM" => "United States Minor Outlying Islands",
    "LB" => "Lebanon",
    "LC" => "Saint Lucia",
    "LA" => "Laos",
    "TV" => "Tuvalu",
    "TW" => "Taiwan",
    "TT" => "Trinidad and Tobago",
    "TR" => "Turkey",
    "LK" => "Sri Lanka",
    "LI" => "Liechtenstein",
    "LV" => "Latvia",
    "TO" => "Tonga",
    "LT" => "Lithuania",
    "LU" => "Luxembourg",
    "LR" => "Liberia",
    "LS" => "Lesotho",
    "TH" => "Thailand",
    "TF" => "French Southern Territories",
    "TG" => "Togo",
    "TD" => "Chad",
    "TC" => "Turks and Caicos Islands",
    "LY" => "Libya",
    "VA" => "Vatican",
    "VC" => "Saint Vincent and the Grenadines",
    "AE" => "United Arab Emirates",
    "AD" => "Andorra",
    "AG" => "Antigua and Barbuda",
    "AF" => "Afghanistan",
    "AI" => "Anguilla",
    "VI" => "U.S. Virgin Islands",
    "IS" => "Iceland",
    "IR" => "Iran",
    "AM" => "Armenia",
    "AL" => "Albania",
    "AO" => "Angola",
    "AQ" => "Antarctica",
    "AS" => "American Samoa",
    "AR" => "Argentina",
    "AU" => "Australia",
    "AT" => "Austria",
    "AW" => "Aruba",
    "IN" => "India",
    "AX" => "Aland Islands",
    "AZ" => "Azerbaijan",
    "IE" => "Ireland",
    "ID" => "Indonesia",
    "UA" => "Ukraine",
    "QA" => "Qatar",
    "MZ" => "Mozambique"
]);