<?php addBackgroundInlineStyle('.background', $backgroundGroup); ?>

<?php if(!empty($image['image_type']) && !empty($image['image'])): ?>
    <div class="casinokollen__header background content-stretchable">

        <?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
        <?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

        <div class="container casinokollen__logo">
            <?php
            if(!empty($image)) {
                $imageArgs = array_merge($image,
                    [
                        'type' => 'small',
                        'alt' => get_the_title()
                    ]
                );
                get_template_part('campaigns/parts/offer-media', null, $imageArgs);
            }
            ?>
        </div>
    </div>
<?php endif; ?>

<div class="container casinokollen__content">
    <?php
    if(!empty($content)) :
        foreach($content as $row): ?>
            <?php if($row['type'] == 'games'): ?>
                <?php
                $gameSlider = [
                    'size' => $row['games_group']['size'],
                    'title' => $row['games_group']['slider_title'],
                    'slides' => $row['games_group']['games_list'],
                    'cta_text' => !empty($row['games_group']['custom_cta_text']) ? $row['games_group']['custom_cta_text'] : get_field_tweaked('play_now', 'option'),
                ];
                get_template_part('components/game-slider', null, $gameSlider); ?>

            <?php endif; ?>

            <?php if($row['type'] == 'offer'): ?>
                <div class="offer-banner">
                    <a class="offer-banner__image" href="<?= $row['offer_group']['button_url']; ?>" title="<?= $row['offer_group']['button_text']; ?>">
                        <img src="<?= $row['offer_group']['image']; ?>" alt="<?= $row['offer_group']['title']; ?>" />
                    </a>
                    <div class="offer-banner__content">
                        <h1 class="offer-banner__title"><?= $row['offer_group']['title']; ?></h1>
                        <div class="offer-banner__description">
                            <?= $row['offer_group']['text']; ?>
                        </div>
                        <div class="offer-banner__btn">
                            <?php
                                $buttonClass = getClass([
                                    'btn',
                                    'btn--rounded',
                                    'btn--small-to-lg',
                                    [
                                        'condition' => !empty($row['offer_group']['button_color']),
                                        'name' => 'btn--' . $row['offer_group']['button_color'],
                                    ]
                                ]);
                            ?>
                            <a class="<?= $buttonClass ?>" href="<?= $row['offer_group']['button_url']; ?>" title="<?= $row['offer_group']['button_text']; ?>"><?= $row['offer_group']['button_text']; ?></a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach;
    endif;
    ?>

    <div class="daily-jackpots">
        <h3 class="daily-jackpots__title"><?= $dailyJackpots['title']; ?></h3>
        <div class="daily-jackpots__description"><?= $dailyJackpots['description']; ?></div>

        <?php get_template_part('components/jackpots-grid', null, ['provider' => JACKPOT_PROVIDE_REDTIGER, 'size' => 'large', 'type' => 'brand']); ?>

    </div>

    <?php if($facebook['show']): ?>
        <div class="facebook">
            <h3 class="facebook__title"><?= $facebook['title']; ?></h3>
            <div class="facebook__description"><?= $facebook['description']; ?></div>
            <a class="facebook__link" href="https://www.facebook.com/<?= $facebook['facebook_id']; ?>" target="_blank" title="@<?= $facebook['facebook_id']; ?>">
                <?= vector('facebook_sqr', 'logos'); ?>
                @<?= $facebook['facebook_id']; ?>
            </a>
        </div>
    <?php endif; ?>
</div>

