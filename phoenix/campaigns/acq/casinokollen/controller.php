<?php
// <PERSON><PERSON><PERSON> (Controller)
// -----------------------------------------------------------------
$backgroundGroup = get_field('background_group');

$image = get_field('image');

global $content;
$content = get_field('content');

$dailyJackpots = get_field('daily_jackpots_content');
$facebook = get_field('facebook_content');

if(!empty($content)) {
    foreach ($content as $key => $row) {
        if ($row['type'] == 'games' && !empty($row['games_group']['enable_casino_games'])) {
            $content[$key]['games_group']['games_list'] = CasinoGame::getGames($row['games_group']['games_list']);
        }
    }
}
