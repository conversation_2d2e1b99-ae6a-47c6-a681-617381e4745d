<?php
/**
 * Improved version of WordPress's core function 'url_to_postid()'
 * which supports custom post types
 *
 * Examines a URL and try to determine the post ID it represents.
 *
 * Checks are supposedly from the hosted site blog.
 *
 * @since 1.0.0
 *
 * @global WP_Rewrite $wp_rewrite WordPress rewrite component.
 * @global WP         $wp         Current WordPress environment instance.
 *
 * @param string $url Permalink to check.
 * @return int Post ID, or 0 on failure.
 */
function from_url_to_postid( $url ) {
	global $wp_rewrite;

	/**
	 * Filters the URL to derive the post ID from.
	 *
	 * @since 2.2.0
	 *
	 * @param string $url The URL to derive the post ID from.
	 */
	$url = apply_filters( 'from_url_to_postid', $url );

	$url_host = parse_url( (string) $url, PHP_URL_HOST );

	if ( is_string( $url_host ) ) {
		$url_host = str_replace( 'www.', '', $url_host );
	} else {
		$url_host = '';
	}

	$home_url_host = parse_url( home_url(), PHP_URL_HOST );

	if ( is_string( $home_url_host ) ) {
		$home_url_host = str_replace( 'www.', '', $home_url_host );
	} else {
		$home_url_host = '';
	}


	// Bail early if the URL does not belong to this site.
	if ( $url_host && $url_host !== $home_url_host ) {
		return 0;
	}

	// First, check to see if there is a 'p=N' or 'page_id=N' to match against.
	if ( preg_match( '#[?&](p|page_id|attachment_id)=(\d+)#', (string) $url, $values ) ) {
		$id = absint( $values[2] );
		if ( $id ) {
			return $id;
		}
	}

	// Get rid of the #anchor.
	$url_split = explode( '#', (string) $url );
	$url       = $url_split[0];

	// Get rid of URL ?query=string.
	$url_split = explode( '?', $url );
	$url       = $url_split[0];

	// Set the correct URL scheme.
	$scheme = parse_url( home_url(), PHP_URL_SCHEME );
	$url    = set_url_scheme( $url, $scheme );

	// Add 'www.' if it is absent and should be there.
	if ( str_contains( home_url(), '://www.' ) && ! str_contains( $url, '://www.' ) ) {
		$url = str_replace( '://', '://www.', $url );
	}

	// Strip 'www.' if it is present and shouldn't be.
	if ( ! str_contains( home_url(), '://www.' ) ) {
		$url = str_replace( '://www.', '://', $url );
	}

	if ( trim( $url, '/' ) === home_url() && 'page' === get_option( 'show_on_front' ) ) {
		$page_on_front = get_option( 'page_on_front' );

		if ( $page_on_front && get_post( $page_on_front ) instanceof WP_Post ) {
			return (int) $page_on_front;
		}
	}

	// Check to see if we are using rewrite rules.
	$rewrite = $wp_rewrite->wp_rewrite_rules();

	// Not using rewrite rules, and 'p=N' and 'page_id=N' methods failed, so we're out of options.
	if ( empty( $rewrite ) ) {
		return 0;
	}

	// Strip 'index.php/' if we're not using path info permalinks.
	if ( ! $wp_rewrite->using_index_permalinks() ) {
		$url = str_replace( $wp_rewrite->index . '/', '', $url );
	}

	if ( str_contains( trailingslashit( $url ), home_url( '/' ) ) ) {
		// Chop off http://domain.com/[path].
		$url = str_replace( home_url(), '', $url );
	} else {
		// Chop off /path/to/blog.
		$home_path = parse_url( home_url( '/' ) );
		$home_path = $home_path['path'] ?? '';
		$url       = preg_replace( sprintf( '#^%s#', preg_quote( $home_path ) ), '', trailingslashit( $url ) );
	}

	// Trim leading and lagging slashes.
	$url = trim( (string) $url, '/' );

	$request              = $url;
	$post_type_query_vars = [];

	foreach ( get_post_types( [], 'objects' ) as $post_type => $t ) {
		if ( ! empty( $t->query_var ) ) {
			$post_type_query_vars[ $t->query_var ] = $post_type;
		}
	}

	// Look for matches.
	$request_match = $request;
	foreach ( (array) $rewrite as $match => $query ) {

		/*
		 * If the requesting file is the anchor of the match,
		 * prepend it to the path info.
		 */
		if ( ! empty( $url ) && ( $url !== $request ) && str_starts_with( $match, $url ) ) {
			$request_match = $url . '/' . $request;
		}

		if ( preg_match( "#^$match#", $request_match, $matches ) ) {

			if ( $wp_rewrite->use_verbose_page_rules && preg_match( '/pagename=\$matches\[([0-9]+)\]/', (string) $query, $varmatch ) ) {
				// This is a verbose page match, let's check to be sure about it.
				$page = get_page_by_path( $matches[ $varmatch[1] ] );
				if ( ! $page ) {
					continue;
				}

				$post_status_obj = get_post_status_object( $page->post_status );
				if ( ! $post_status_obj->public && ! $post_status_obj->protected
					&& ! $post_status_obj->private && $post_status_obj->exclude_from_search ) {
					continue;
				}
			}

			/*
			 * Got a match.
			 * Trim the query of everything up to the '?'.
			 */
			$query = preg_replace( '!^.+\?!', '', (string) $query );

			// Substitute the substring matches into the query.
			$query = addslashes( WP_MatchesMapRegex::apply( $query, $matches ) );

			// Filter out non-public query vars.
			global $wp;
			parse_str( $query, $query_vars );
			$query = [];
			foreach ( (array) $query_vars as $key => $value ) {
				if ( in_array( (string) $key, $wp->public_query_vars, true ) ) {
					$query[ $key ] = $value;
					if ( isset( $post_type_query_vars[ $key ] ) ) {
						$query['post_type'] = "any";
						$query['name']      = $value;
					}
				}
			}

			// Resolve conflicts between posts with numeric slugs and date archive queries.
			$query = wp_resolve_numeric_slug_conflicts( $query );

			//////////// ADDITION FOR CUSTOM POST TYPE SUPPORT BEGINS
			GLOBAL $wpdb;
			$post_types_query = 'SELECT DISTINCT post_type FROM ' . $wpdb->posts;
			$post_types = $wpdb->get_results( $post_types_query, ARRAY_N );
			$query['post_type'] = array_column($post_types,'0');
			//////////// ADDITION FOR CUSTOM POST TYPE SUPPORT ENDS

			// Do the query.
			$query = new WP_Query( $query );

			if ( ! empty( $query->posts ) && $query->is_singular ) {
				return $query->post->ID;
			} else {
				return 0;
			}
		}
	}
	return 0;
}

// Improved wp_die handler for better wordpress error pages
function die_in_beauty($message, $title, $args)
{
	if (empty($title)) {
		$title = 'Error';
	}

	if (function_exists('is_wp_error') && is_wp_error($message)) {
		$errors = $message->get_error_messages();
		switch (count($errors)) {
			case 0:
				$message = '';
				break;
			case 1:
				$message = $errors[0];
				break;
			default:
				$message = "<ul>\n\t\t<li>" . join("</li>\n\t\t<li>", $errors) . "</li>\n\t</ul>";
				break;
		}
	} else {
		$message = strip_tags($message);
	}
	get_header(); ?>

	<body class="<?= getBodyClass('article page'); ?>">
		<?php get_template_part('components/after-body'); ?>

		<?php get_template_part('components/header-layout'); ?>

		<div class="background">
			<article class="not-found-404">
				<h1><?php echo $title; ?></h1>
				<h3><?php echo apply_filters('the_content', $message); ?></h3>
			</article>
		</div>

		<script>
			setTimeout(function() {
				window.location.href = "<?= brandUrl(); ?>"
			}, 3000);
		</script>

	<?php get_footer(null, ['no-layout' => true]);
	die();
}

add_filter('wp_die_handler', 'die_in_beauty_filter');
function die_in_beauty_filter($die_handler)
{
	if (is_admin()) {
		return $die_handler;
	}
	return 'die_in_beauty';
}

function redirect_to_404() {
	wp_redirect(home_url('404'));
	exit();
}

/**
 * Search Tweak
 *
 * @param string   $search Search SQL for WHERE clause.
 * @param WP_Query $wp_query   The current WP_Query object.
 */
add_filter( 'posts_search', 'search_tweak', 10, 2 );
function search_tweak( $search, $wp_query ) {

    // Bail if we are not in the admin area
    if ( ! is_admin() ) {
        return $search;
    }

    // Bail if this is not the search query.
    if ( ! $wp_query->is_main_query() && ! $wp_query->is_search() ) {
        return $search;
    }

    // Get the value that is being searched.
    $search_string = get_query_var( 's' );

    // Bail if the search string is not an integer.
    if ( ! filter_var( $search_string, FILTER_VALIDATE_INT ) ) {

		if(!empty($search_string) && !is_admin()) {
			// Redirect to 404 if Blog is not enabled
			if (!isFeatureActive('blog')) {
				redirect_to_404();
			} else {
				$post_count = wp_count_posts();

				// Redirect to 404 if there's no 5 published blog posts at least
				if($post_count->publish < 5) {
					redirect_to_404();
				}
			}
		}

        return $search;
    }

	global $wpdb;
	/*
	* 	Ability to search by post ID on WP Admin.
    *	Return modified posts_search clause.
	*/
    return "AND $wpdb->posts.ID = '" . intval( $search_string )  . "'";
}

function no_cpt_on_archives_and_search( $query ) {
	// Don't show custom post types on search results page
	if (!$query->is_admin && $query->is_search) {
		$query->set('post_type', array('post', /* 'page', */));
    }

	// Don't show custom post types on archive pages
	// TODO: Fix since it breaks /quiz/ archive page
	// if ( $query->is_archive() && $query->is_main_query() ) {
	// 	$query->set('post_type', array('post', /* 'page', */));
	// }
}

add_action( 'pre_get_posts', 'no_cpt_on_archives_and_search' );

// Get menus in a structured way to make render(view) code have better readability
function wp_get_menu_array($current_menu)
{
	$array_menu = wp_get_nav_menu_items($current_menu);
	$menu = array();
	foreach ($array_menu as $m) {
		if (empty($m->menu_item_parent)) {
			$menu[$m->ID] = array();
			$menu[$m->ID]['ID']          = $m->ID;
			$menu[$m->ID]['title']       = $m->title;
			$menu[$m->ID]['description'] = $m->description;
			$menu[$m->ID]['url']         = $m->url;
			$menu[$m->ID]['classes']     = $m->classes;
			$menu[$m->ID]['children']    = array();
		}
	}
	$subMenu = array();
	foreach ($array_menu as $m) {
		if ($m->menu_item_parent) {
			$subMenu[$m->ID] = array();
			$subMenu[$m->ID]['ID']          = $m->ID;
			$subMenu[$m->ID]['title']       = $m->title;
			$subMenu[$m->ID]['description'] = $m->description;
			$subMenu[$m->ID]['url']         = $m->url;
			$subMenu[$m->ID]['classes']     = $m->classes;
			$menu[$m->menu_item_parent]['children'][$m->ID] = $subMenu[$m->ID];
		}
	}
	return $menu;
}

class Phnx_Walker_Nav_Menu extends Walker_Nav_Menu
{

	function start_lvl(&$output, $depth = 0, $args = array())
	{
		$indent = str_repeat("\t", $depth);
		$output .= "\n$indent<ul class=\"navigation__menu__submenu\">\n";
	}

	function start_el(&$output, $item, $depth = 0, $args = array(), $id = 0)
	{

		global $wp_query;
		$indent = ($depth) ? str_repeat("\t", $depth) : '';

		$class_names = $value = '';

		$classes = empty($item->classes) ? array() : (array) $item->classes;
		$classes[] = 'menu-item-' . $item->ID;

		$class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));

		// Check our custom has_children property.here is the points
		if (in_array('menu-item-has-children', $classes) && $depth == 0) {
			// Your Code
			$class_names = ' class="dropdown custom-drop ' . esc_attr($class_names) . '"';
		} else {
			$class_names = ' class="' . esc_attr($class_names) . '"';
		}

		$id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
		$id = strlen($id) ? ' id="' . esc_attr($id) . '"' : '';

		$output .= $indent . '<li' . $id . $value . $class_names . '>';

		$attributes  = !empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) . '"' : '';
		$attributes .= !empty($item->target)     ? ' target="' . esc_attr($item->target) . '"' : '';
		$attributes .= !empty($item->xfn)        ? ' rel="'    . esc_attr($item->xfn) . '"' : '';
		$attributes .= !empty($item->url)        ? ' href="'   . esc_attr($item->url) . '"' : '';

		$item_output = $args->before;

		$item_output .= '<a' . $attributes . '>';
		$item_output .= $args->link_before . apply_filters('the_title', $item->title, $item->ID) . $args->link_after;
		// Check our custom has_children property.here is the points
		if (in_array('menu-item-has-children', $classes) && $depth == 0) {
			// Dropdown chevron (bottom.svg)

			if(!empty( $args->theme_location) && $args->theme_location !== 'mobile-menu') {
				$item_output .= vector('dropdown-down');
			}
		}
		$item_output .= '</a>';
		$item_output .= $args->after;

		$output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
	}
}

function roundUp($n, $x = 5) {
    return (round($n) % $x === 0) ? round($n) : round(($n + $x / 2 ) / $x ) * $x;
}
