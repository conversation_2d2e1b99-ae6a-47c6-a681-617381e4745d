<?php
// ACF related

if (! file_exists(get_template_directory() . '/vendor/advanced-custom-fields-pro/acf.php') && !class_exists('acf')) {
    add_action('admin_notices', function () {
        $class          = 'notice notice-error';
        $notice_message = 'ACF Pro plugin file not found in path phoenix/vendor/advanced-custom-fields-pro/acf.php. Please install ACF Pro either in vendor folder or WordPress plugins folder.';
        printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), esc_html($notice_message));
    }
    );
    return;
}

if(!class_exists('acf')) {
    // Define path and URL to the ACF plugin.
    define('MY_ACF_PATH', get_template_directory() . '/vendor/advanced-custom-fields-pro/');
    define('MY_ACF_URL', get_template_directory_uri() . '/vendor/advanced-custom-fields-pro/');

    // Include the ACF plugin.
    include_once( MY_ACF_PATH . 'acf.php' );


    // Customize the url setting to fix incorrect asset URLs.
    add_filter('acf/settings/url', 'my_acf_settings_url');
    function my_acf_settings_url( $url ) {
        return MY_ACF_URL;
    }
}

// Save ACF to JSON
add_filter('acf/settings/save_json', 'save_acf');
function save_acf($path)
{
    $path = get_template_directory() . '/acf-json';
    return $path;
}

// Load ACF from json
add_filter('acf/settings/load_json', 'load_acf_parent');
function load_acf_parent($paths)
{
    unset($paths[0]);
    $paths[] = get_template_directory() . '/acf-json';
    return $paths;
}

// Load ACF from child theme json
add_filter('acf/settings/load_json', 'load_acf_child');
function load_acf_child($paths)
{
    unset($paths[0]);
    $paths[] = get_stylesheet_directory() . '/acf-json';
    return $paths;
}
