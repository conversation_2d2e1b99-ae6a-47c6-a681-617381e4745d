/* Lint ------------------- */
/* global main_params, dataLayerHandler */

/*

Tracking API below returns information about logged-in user,
if visitor is not logged in then it returns general info like region and currency based on visitor's IP address

*/
async function fetchTrackingWP() {
    let templateName,
        isGoSite = main_params.site == ('go'),
        isRgSite = main_params.site == ('rg'),
        isFaqSite = main_params.site == ('faq');

    let url = `${main_params.brand_url}/tracking/?frame=wp`;
    const controller = new AbortController();
    const signal = controller.signal;
    let bodyClasses = document.body.classList;
    let isPage = bodyClasses.contains('article') || bodyClasses.contains('page');
    let isDynamic = bodyClasses.contains('dynamic');
    let isStartPage = bodyClasses.contains('start-page');
    let isPNP = bodyClasses.contains('pnp');
    templateName = bodyClasses[1];

    // handle templates: dynamic, dynamic-start-page, normal-start-page
    templateName = (isDynamic ? "dynamic" :  templateName);
    templateName = (isStartPage ? (isPNP ? bodyClasses[2] : bodyClasses[1]) + "-start-page" : templateName);

    // handle pages: e.g. page-go, page-rg, page-faq
    templateName = (isPage ? "page-go" : templateName); // hardcode fallback for Go pages
    templateName = (isPage && (isGoSite || isRgSite || isFaqSite) ? "page-" + main_params.site : templateName);

    setTimeout(() => controller.abort(), 2000);

    const response = await fetch(url, {
            signal,
            method: "GET",
        })
        .then((res) => res.json())
        .catch(() => {
            // When tracking API url couldn't be fetched, (e.g. API is down, or domain was restricted by a country etc.)
            if (main_params.debug == "1") {
                console.log("Tracking not initialized");
            }
        });

    if (response?.result) {
        if (response.result.eventInfo !== null && typeof response.result.eventInfo !== 'undefined') {
            let eventInfo = JSON.parse(response.result.eventInfo.replace(/'/g, '"'));
            eventInfo['event'] = 'initial-meta';
            eventInfo['frame'] = 'wp';
            eventInfo['pixel_language'] = main_params.language;
            eventInfo['wp_template'] = templateName;

            dataLayerHandler(eventInfo);
        }
    }
}