// Compliance header
/* Lint ------------------- */
/* global documentReady, getCookie, postJson, notifications, focusLost, disableScroll */
var sessionTimestamp;

/**
 * Display the timer for the user session
 *
 * @param {*} selector: dom class for selecting element
 * @param {number} sessionTimestamp: timestamp when user logged in (in milliseconds)
 * @param {*} length: length of the string to display the time 8 for H:i:s or 5 for H:i
 */
function setSessionTimer(selector, sessionTimestamp, length = 8) {
    let difference = (new Date().getTime() - sessionTimestamp),
        hours      = Math.floor(difference / 36e5),
        minutes    = Math.floor(difference % 36e5 / 60000),
        seconds    = Math.floor(difference % 60000 / 1000),
        time       = ('0' + hours).slice(-2) + ':' + ('0' + minutes).slice(-2) + ':' + ('0' + seconds).slice(-2);
    document.querySelector(selector).innerHTML = time.slice(0, length);
}

/**
 * Display the Casino Net position for the user
 */
function setCasinoNetPosition(selector) {
    // Set balance only if server-side rendering (the data coming via /player/currentSessionInfo) could not render it
    if(document.querySelector(selector).innerHTML.length == 0) {
        // Check player-balance cookie first
        if (getCookie('player-balance') != false) {
            let casinoNetPosition = parseFloat(getCookie('player-balance')).toFixed(2);
            document.querySelector(selector).innerHTML = casinoNetPosition;
        }
        // Check another cookie named casinoNetPosition
        else if (getCookie('casinoNetPosition') != false) {
            let casinoNetPosition = parseFloat(getCookie('casinoNetPosition')).toFixed(2);
            document.querySelector(selector).innerHTML = casinoNetPosition;
        }
        else {
            let casinoNetPosition = parseFloat(0).toFixed(2);
            document.querySelector(selector).innerHTML = casinoNetPosition;
        }
    }
}

var panicButton;
var panicButtonBackground;
var panicButtonWidth;
var panicButtonPaddingLeft;
var panicButtonDragable
var MouseInitialX;
var panicButtonActive  = false;
var accountBlocked     = false;
var panicButtonPressed = false;
var panicToken;

function panicButtonDragStart(e) {
    if (!accountBlocked) {
            panicButtonPressed     = true;
        let panicButtonStyle       = getComputedStyle(panicButton);
            panicButtonWidth       = parseInt(panicButtonStyle.width);
            panicButtonPaddingLeft = panicButtonStyle.paddingLeft;
            MouseInitialX          = e.pageX;
        if (e.type === "touchstart") {
            MouseInitialX = e.touches[0].pageX;
        }
    }
}

function panicButtonDragEnd(e) {
    if (panicButtonPressed) {
        panicButtonPressed = false;
        if (panicButtonActive) {
            lockAccount();
        } else {
            movePanicButton(0);
        }
    } else {
        movePanicButton(0);
    }
}

function panicButtonDrag(e) {
    if (panicButtonPressed && !panicButtonActive) {
        let MouseCurrentX = e.pageX;
        if (e.type === "touchmove") {
            MouseCurrentX = e.touches[0].pageX;
        }
        let panicButtonPosition = MouseCurrentX - MouseInitialX;

        if (panicButtonPosition < 0) {
            panicButtonPosition = 0;
        }

        if (panicButtonPosition > panicButtonWidth ) {
            panicButtonPosition = panicButtonWidth;
            panicButtonActive   = true;
        }

        movePanicButton(panicButtonPosition);
    }
}

function movePanicButton(position = 0) {
    panicButtonDragable.style.left = `calc(${position}px + ${panicButtonPaddingLeft})`;
}

function setPanicButtonBackgroundWidth() {
    const minAreaPanicButton                = window.innerWidth * window.innerHeight * 0.026;                                // using 2.6% of viewportarea to reduce edge cases
    let   panicButtonBackgroundStyle        = getComputedStyle(panicButtonBackground);
    let   minWidthPanicButtonContainer      = Math.round(minAreaPanicButton / parseInt(panicButtonBackgroundStyle.height));  // 40 and 60 Figma fixed height of slider
          minWidthPanicButtonContainer      = minWidthPanicButtonContainer >= 202 ? minWidthPanicButtonContainer : 202;
          panicButtonBackground.style.width = `${minWidthPanicButtonContainer}px`;
}

function lockAccount() {
    postJson(`/player-account/close`, {
        key: 'ACCOUNT_CLOSING_REASON_RG_TEMP_TIMEOUT',
        value: {
            oasisReason: 'RES_GAMING_CLOSE_24HOURS'
        }
    }, `${protocol}${domain}`)
    .then(response => {
        if (response.status === 'SUCCESS') {
            panicToken     = response.result;
            accountBlocked = true;
            panicButton.classList.add('panic-btn--locked');
            window.location.hash = 'panic-reason';
            window.dispatchEvent(new HashChangeEvent("hashchange"));
        } else {
            movePanicButton(0);
            panicButtonActive = false;
        }
    })
        .catch((reason) => {
            console.error(reason);
            movePanicButton(0);
            panicButtonActive = false;
        });
}

function sendPanicReason(e) {
    e.preventDefault();

    const form = new FormData(e.target);
    const panicReason = form.get("panic_reason");

    if (panicToken && panicReason) {
        postJson(`/player-account/panic-button-reason`, {
            token : panicToken,
            reason: panicReason
        }, `${protocol}${domain}`)
        .then(response => {
            window.location.hash = '/';
            window.dispatchEvent(new HashChangeEvent("hashchange"));

            let notification = notifications.get('alert_user_locked_out');
            notification.show();
        })
        .catch(reason => console.error(reason));
    }

    return false;
}

function resizeHandlerPanicButton() {
    setPanicButtonBackgroundWidth();
    panicButtonDragEnd();
}

const domain = window.location.hostname.split('.').slice(-2).join('.'),
        protocol = window.location.protocol + '//www.';

////////////////////////////////////////////////////////////////////////////////////

documentReady(function () {
    // If compliance header is not active
    if (getCookie('loggedInTime') != false) {
        sessionTimestamp = parseInt(getCookie('loggedInTime'));
    }
    if (document.querySelector('.js-session-timer-seconds')) {
        // Compliance header timer
        if (sessionTimestamp) {
            setSessionTimer('.js-session-timer-seconds', sessionTimestamp);
            setInterval(() => setSessionTimer('.js-session-timer-seconds', sessionTimestamp), 1000);
        }
    } else if (document.querySelector('.js-session-timer-minutes')) {
        // Compliance header timer
        if (sessionTimestamp) {
            setSessionTimer('.js-session-timer-minutes', sessionTimestamp, 5);
            setInterval(() => setSessionTimer('.js-session-timer-minutes',sessionTimestamp, 5), 10000);
        }
    } else {
        // Dynamic theme color
        // if($('body').hasClass('start-page')) {
        //     document.querySelector('[name="theme-color"]').attributes['content'].value = window.getComputedStyle(document.querySelector('header.navigation')).getPropertyValue('background-color');
        // } else {
        // Check first if element exists since vanilla js throws error if element not found
        if (document.querySelector('[name="theme-color"]') && document.querySelector('header .navigation')) {
            document.querySelector('[name="theme-color"]').attributes['content'].value = window.getComputedStyle(document.querySelector('header .navigation')).getPropertyValue('background-color');
        }
        // }
    }

    if (document.querySelector('.js-casino-net-position')) {
        setInterval(() => setCasinoNetPosition('.js-casino-net-position'), 1000);
    }

    if (document.querySelector(".js-panic-btn")) {
        panicButton           = document.querySelector(".js-panic-btn");
        panicButtonDragable   = document.querySelector(".js-panic-btn-dragable");
        panicButtonBackground = document.querySelector(".js-panic-btn-background");

        setPanicButtonBackgroundWidth();

        panicButtonDragable.addEventListener("touchstart", panicButtonDragStart, false);
        window.addEventListener("touchend", panicButtonDragEnd, false);
        window.addEventListener("touchmove", panicButtonDrag, false);

        panicButtonDragable.addEventListener("mousedown", panicButtonDragStart, false);
        window.addEventListener("mouseup", panicButtonDragEnd, false);
        window.addEventListener("mousemove", panicButtonDrag, false);

        window.addEventListener('resize', resizeHandlerPanicButton, false);
    }

    // If NL compliance header exists
    if (document.querySelector('.compliance-header--ksa')) {
        // Refresh the page when tab loses focus
        let showRefreshMessage = true;
        focusLost(showRefreshMessage);
    }
});