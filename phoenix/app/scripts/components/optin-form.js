// Optin Form showing the CTA button after successful ajax call
/* Lint ------------------- */
/* global documentReady */
documentReady(function () {
    if (document.querySelector('.js-optin-form')) {
		var origOpen = XMLHttpRequest.prototype.open;
		XMLHttpRequest.prototype.open = function () {
			this.addEventListener('load', function () {
				if (/"status":"optin-saved"/i.test(this.responseText)) {
					document.querySelectorAll('.js-optin-form').forEach((form) => {
						form.style.display = 'none';
					});
					document.querySelectorAll('.js-optin-offer-cta').forEach((cta) => {
						cta.style.display = 'block';
					});
				}
			});
			origOpen.apply(this, arguments);
		};
	}
});