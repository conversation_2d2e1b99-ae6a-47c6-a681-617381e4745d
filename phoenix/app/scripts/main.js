// main.js
// Should contain ONLY important helper functions
/* global documentReady, dataLayer, main_params */

// Disables the scroll for the user
let disableScroll = () => {
    document.body.classList.add('no-scroll');
};

// Enables the scroll for the user
let enableScroll = () => {
    document.body.classList.remove('no-scroll');
};

// documentReady function
function documentReady(fn) {
    // see if DOM is already available
    if (document.readyState === "complete" || document.readyState === "interactive") {
        // call on next available tick
        setTimeout(fn, 1);
    } else {
        document.addEventListener("DOMContentLoaded", fn);
    }
}

// Get cookie value if exists
function getCookieDomain() {
    let domain = location.hostname.split('.').slice(-2).join('.'); // get main domain only
    if (domain != 'localhost') {
        domain = `.${domain}`;
    }
    return domain;
}

function getCookie(name) {
    function escape(s) { return s.replace(/([.*+?^$(){}|[\]/\\])/g, '\\$1'); }
    var match = document.cookie.match(RegExp('(?:^|;\\s*)' + escape(name) + '=([^;]*)'));
    return match ? match[1] : null;
}

function setCookie(name, value, expires, path = '/') {
    let domain = getCookieDomain();
    document.cookie = `${name}=${value};expires=${expires};domain=${domain};path=${path}`;
}

function deleteCookie(name, path = '/') {
    let domain = getCookieDomain();
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=${domain};path=${path}`;
}

// Check if an element is in current viewport
$.fn.isInViewport = function() {
    if(typeof $(this).offset() != typeof undefined) {
        var elementTop = $(this).offset().top;
        var elementBottom = elementTop + $(this).outerHeight();
        var viewportTop = $(window).scrollTop();
        var viewportBottom = viewportTop + $(window).height();
        return elementBottom > viewportTop && elementTop < viewportBottom;
    } else {
        return true;
    }
};

// Filter url when there's a tracking parameter found (returns allowed tracking params only)
function urlParamsFiltered() {
    let urlParams = new URLSearchParams(window.location.search), // Get all query string params
        // trackingParams = ['aff', 'fp', 'fmb', 'transaction_id', 'cid', 'clickid', 'var', 'mid', 'sid', 'aid', 'pid'], // Allowed click tracking params
        doesUrlContainTrackingParam = false; // Fallback

    main_params.tracking.forEach(function (param){
        if (urlParams.has(param)) {
            doesUrlContainTrackingParam = true;
        }
    })

    // Remove disallowed params if url contains any allowed tracking parameter
    if (doesUrlContainTrackingParam) {
        let disallowedParams = [];
        for (let [key, value] of urlParams) {
            if(!main_params.tracking.includes(key)) {
                disallowedParams.push(key);
            }
        }
        // urlParams.delete(key) not works properly in for loop above, therefore we are using it in foreach loop below
        disallowedParams.forEach(param => urlParams.delete(param));
        urlParams = urlParams.toString();
    } else {
        urlParams = doesUrlContainTrackingParam;
    }
    return urlParams;
}

// Run animation
let runAnimation = function (el, animation) {
    el.classList.add(animation);
    window.setTimeout(function () {
        el.classList.remove(animation);
    }, 1000);
}

// Checks if an element has the class
function hasClass(ele, cls) {
    return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'));
}

// Adds class to an element
function addClass(ele, cls) {
    if (!hasClass(ele, cls)) ele.className += " " + cls;
}

// Removes class from an element
function removeClass(ele, cls) {
    if (hasClass(ele, cls)) {
        var reg           = new RegExp('(\\s|^)' + cls + '(\\s|$)');
            ele.className = ele.className.replace(reg, ' ');
    }
}

// Updates current location url with given anchorlink (hash). example definition for variable -> const newHash = "#registration"
// If no variable sent, then it will clean the anchorlink (hash) from the current location url
function updateHash(newHash = "") {
    if ("pushState" in history)
        if (newHash !== "") {
            history.pushState({}, "", newHash);
        } else {
            history.pushState("", document.title, window.location.pathname + window.location.search);
        }
    else {
        window.location.hash = newHash;
    }
}

// Updates height of iframe, takes iframe element as parameter
function iframeHeightHandler(iframe) {
    // Find the height of inner page
    var doc                     = iframe.contentDocument ? iframe.contentDocument : iframe.contentWindow.document;
    var heighOfIframe           = doc.body.scrollHeight;

    // IE opt. which needs a bit added or scrollbar appears
    iframe.style.height     = heighOfIframe + 4 + "px";
    iframe.style.visibility = 'visible';
}

// DataLayer handler function
function dataLayerHandler(data) {
    window.dataLayer = window.dataLayer || [];

    if (typeof dataLayer != 'undefined') {
        dataLayer.push(data);

        if (main_params.dev == "1" && main_params.debug == "1") {
            console.table(data);
        }
    }
}

// Check if user has left the tab and came back
function focusLost(showRefreshMessage = false) {
    window.addEventListener('visibilitychange', () => {
        // Show "Page is getting refreshed. Please wait..." message in overlay
        if (showRefreshMessage) {
            disableScroll();
            document.querySelector('#refresh-overlay.overlay-module').classList.add('visible');
        }

        if (document.visibilityState === 'visible') window.location.reload();
    })
}

function getRandomInt (min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min) + min); // The maximum is exclusive and the minimum is inclusive
}

// Simulated date time
// get the difference between the real datetime and the simulated one and keep that diff when calling the getDateTimeNow()
var simulated_time_diff;
window.addEventListener('load', () => {
    if (main_params.simulated_time) {
        simulated_time_diff = new Date(main_params.simulated_time).getTime() - new Date().getTime();
    }
})

function getDateTimeNow() {
    let now = new Date();
    if (main_params.simulated_time) {
        return new Date(now.getTime() + simulated_time_diff);
    }

    return now;
}

function capitalize(string) {
    return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
}