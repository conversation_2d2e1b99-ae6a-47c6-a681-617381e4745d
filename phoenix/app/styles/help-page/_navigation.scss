.navigation.navigation--help-page {
    display: flex;

    .navigation {
        &__container {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            width: 100%;

            @extend %container;
        }

        &__logo {
            margin-right: auto;
        }

        &__back {
            display: none;
            padding: 0 $gutter;

            a {
                text-decoration: none;
                @include get-typography('label');
                color: $color-font-light;
            }
        }

        &__language {
            display: none;
        }

        &__search {
            &--mobile {
                svg {
                    fill: $color-font-light;
                    height: 24px;
                    width: 24px;
                }

                path {
                    fill: $color-font-light;
                }

                @include from(sm) {
                    display: none;
                }
            }

            &--desktop {
                display: none;
                @include from(sm) {
                    display: block;
                }
            }

        }
    }
}

// showing in navigation search or back to comeon link depending on the page
body.post-type-archive-help-page,
body.help-page-contact {
    .navigation.navigation--help-page {
        .navigation {
            &__search {
                display: none;
            }

            &__back {
                display: block;
            }
        }
    }
}
