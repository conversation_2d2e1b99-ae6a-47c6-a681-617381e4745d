.navigation {
    &__container {
        @extend %container;

        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        line-height: 0;
        position: relative;

        @include to(lg) {
            padding: 0;
        }

        @include from(lg) {
            display: flex;
            @include gap(12px);
        }

        &--secondary {
            display: block;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;

            @include from(md) {
                overflow: visible;
            }

            @include scrollbar(0);
        }

        &--promo {
            .navigation__logo {
                @include to(lg) {
                    margin-left: 12px;
                }
            }
        }
    }

    &__menu-mobile {
        @include from(lg) {
            display: none;
        }

        a {
            box-sizing: border-box;
            display: inline-block;
            padding: 8px 10px;
            user-select: none;
            text-align: center;
            width: 100%;
            -webkit-touch-callout: none; /* iOS Safari */
            -webkit-user-select: none; /* Safari */

            @include between(md, lg) {
                padding: 18px 10px;
            }

            svg {
                fill: $header-icon;
                height: 28px;
            }

            path {
                fill: $header-icon;
            }
        }
    }

    &__login-mobile {
        display: flex;
        margin-left: auto;
        justify-self: end;
        align-items: center;

        @include from(lg) {
            display: none;
        }
    }

    &__account-mobile {
        box-sizing: border-box;
        display: inline-block;
        padding: 8px 10px;
        user-select: none;
        -webkit-touch-callout: none; /* iOS Safari */
        -webkit-user-select: none; /* Safari */

        @include between(md, lg) {
            padding: 18px 10px;
        }

        @include get-typography("label");
        max-width: 120px;
        text-align: right;
        text-decoration: none;
        width: auto;
        color: $header-icon;
    }

    &__cta-mobile {
        margin-right: 12px;
    }

    &__logo {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin-right: 24px;

        svg {
            float: left;
            height: 44px;
            margin: 10px 0;

            @include to(lg) {
                float: none;
                margin: 0;
            }
        }

        img {
            max-height: 48px;
            width: 100%;
        }
    }

    &__plus-18 {
        @include to(lg) {
            margin-right: 12px;
        }
    }

    &__menu {
        display: none;

        @include from(lg) {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: stretch;
            justify-content: center;
            height: $header-height;
        }

        ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;

            &:nth-of-type(2) {
                float: right;
            }

            li {
                position: relative;

                @include between(lg, xl) {
                    // Show only first 4 links until 1200px
                    &:nth-child(n + 5) {
                        display: none;
                    }
                }

                @include between(xl, xxl) {
                    // Show only first 6 links until 1440px
                    &:nth-child(n + 7) {
                        display: none;
                    }
                }

                &:hover {
                    cursor: pointer;

                    :after {
                        content: "";
                        display: block;
                        width: 100%;
                        height: 3px;
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        background: $header-active-border;

                        @include to(md) {
                            width: calc(100% - 28px);
                            left: 14px;
                            border-radius: 20px;
                            background: $global-nav-active-color;
                        }
                    }

                    @include to(md) {
                        a {
                            color: $global-nav-active-color;
                        }
                    }

                    // Convert chevrown-down to chevron-up on desktop view
                    & > svg {
                        path {
                            transform: rotate(180deg);
                            transform-origin: 50% 50%;
                        }
                    }
                }

                svg {
                    margin-left: 6px;
                    fill: $header-icon;

                    path {
                        fill: $header-icon;
                    }
                }

                &:first-child {
                    margin-left: 0;
                }

                &.current-menu-item,
                &.current-menu-ancestor,
                &.current_page_item {
                    &::after {
                        content: "";
                        display: block;
                        width: 100%;
                        height: 3px;
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        background: $header-active-border;
                    }
                }

                a {
                    padding: 0 12px;
                    color: $header-color;
                    display: inline-block;
                    @include get-typography("label-bold");
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    text-decoration: none;
                }

                &.menu-item-has-children {
                    padding-right: 12px;

                    &:hover > ul {
                        display: block;
                        height: auto;
                    }

                    a {
                        padding-right: 0;
                    }

                    ul {
                        display: none;

                        &:after {
                            display: none;
                        }

                        li {
                            /* First Tier Dropdown */
                            width: 100%;
                            float: none;
                            display: list-item;
                            position: relative;
                            margin: 0;
                            border-bottom: 1px solid lighten($header-background, 10%);
                            padding-right: 0;

                            &:hover {
                                border-bottom: 1px solid lighten($header-background, 10%);
                                margin-top: 0;
                            }

                            &:after {
                                display: none !important;
                                height: 0 !important;
                            }

                            &:last-child,
                            &:last-child:hover {
                                border-bottom: none;
                            }

                            & a {
                                font-weight: normal;
                                line-height: normal;
                                height: auto;
                                margin: 0 auto;
                                padding: 10px 12px;
                                width: calc(100% - 24px);
                                text-align: left;
                                justify-content: start;

                                &:hover {
                                    color: $header-active-border;
                                }

                                &:after {
                                    display: none;
                                }
                            }
                        }
                    }
                }
            }
        }

        /* Hide Dropdowns by Default */
        &__submenu {
            display: none;
            position: absolute;
            bottom: 0;
            transform: translateY(100%);
            left: 0px;
            background: $header-background;
            box-shadow: 0 3px 5px rgba($color-primary-black, 0.2);
            padding: 0;
            margin: 0;
            min-width: 200px;
            z-index: 25;
        }

        &--secondary {
            display: flex;
            justify-content: start;
            height: $secondary-header-height;

            ul li {
                svg {
                    fill: $secondary-header-icon;

                    path {
                        fill: $secondary-header-icon;
                    }
                }

                a {
                    color: $secondary-header-color;
                }

                &.menu-item-has-children {
                    ul {
                        li {
                            /* First Tier Dropdown */
                            border-bottom: 1px solid lighten($secondary-header-background, 10%);

                            &:hover {
                                border-bottom: 1px solid lighten($secondary-header-background, 10%);
                            }
                        }
                    }
                }
            }
        }

        &--secondary &__submenu {
            background: $secondary-header-background;
        }

        &--mobile-tabs {
            display: none;
            height: 40px;

            @include to(lg) {
                display: flex;
                justify-content: center;
            }

            & ul {
                & li {

                    & a {
                        @include get-typography("caption-bold");
                        height: 40px;
                        padding: 0 14px;
                    }
                }
            }
        }
    }
}
