.formpx {
    &.no-labels {
        font-size: 0;
    }

    .formpx__group {
        position: relative;
        margin: 32px 0;
        max-width: 325px;

        @include to(md) {
            max-width: 100%;
        }

        label {
            display: inline-block;
            @include get-typography('label-bold');
            margin: 12px 12px 12px 0;

            span {
                color: $color-system-error;
            }
        }

        .formpx__input {
            max-width: 100%;
        }
    }

    select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .formpx__input {
        @include from(md) {
            .optinform & {
                border-radius: $border-radius-md 0 0 $border-radius-md;
            }

            display: inline-block;
            margin: 0;
            max-width: 65%;
            vertical-align: middle;

            &::placeholder {
                color: $color-font-supportive;
                opacity: 1;
            }
        }

        -moz-appearance: textfield;
        background-color: $input-background;
        border: 0;
        border-top-left-radius: 3px;
        border-top-right-radius: 3px;
        border-bottom: 1px solid $input-border;
        box-sizing: border-box;
        color: $input-color;
        display: block;
        @include get-typography('label');
        line-height: 22px;
        margin-bottom: 12px;
        outline: none;
        padding: 15px 12px;
        transition: all 0.2s ease; // sass-lint:disable-line no-transition-all
        width: 100%;

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        &.wpcf7-not-valid {
            outline: 1px solid $color-system-error;
        }

        &:not(select):focus {
            color: $input-label-active;
            border-color: $input-border-active;
        }
    }

    .formpx__currency {
        position: relative;
        -webkit-user-select: none;
        -ms-user-select: none;
        user-select: none;

        &-symbol {
            color: $color-font-light;
            margin: 16px 0;
            position: absolute;
            right: 10px;
        }
    }

    .formpx__radio {
        appearance: none;
        border: 3px solid transparent;
        border-radius: 50%;
        cursor: pointer;
        outline: 2px solid $color-font-light;
        transition: background 0.3s, border-color 0.3s;
        vertical-align: text-bottom;
        height: 18px;
        width: 18px;

        &:checked {
            background: $input-label-active;
            border-color: inherit;
            outline-color: $input-label-active;
        }
    }

    .formpx__button,
    [type="submit"] { //sass-lint:disable-line no-ids
        @include get-typography('heading-4');

        @include from(md) {
            .optinform & {
                border-radius: 0 $border-radius-md $border-radius-md 0;
            }
            display: inline-block;
            margin: 0;
            max-width: 35%;
            vertical-align: middle;
            width: auto;
        }

        box-sizing: border-box;
        display: block;
        font-weight: bold;
        line-height: 18px;
        padding: 17px 20px;
        text-align: center;
        width: 100%;
    }

    .wpcf7-not-valid-tip {
        color: $color-system-error;
    }

    .wpcf7-list-item {
        margin: 10px 0 0;
    }
}

.formpx__message { //sass-lint:disable-line no-ids
    opacity: 0;
    height: 0;
    transform: scale(0);
    transition: $transition;

    p {
        border: 0;
        border-radius: 0;
        color: $color-font-light;
        margin: 0;
        padding: 16px 12px;
        text-align: center;
        @include get-typography('label-bold');


        &.error {
            background-color: $color-system-error;
        }

        &.success {
            background-color: $color-system-success;
        }
    }
}

.notification-appears {
    opacity: 1;
    height: auto;
    transform: scale(1);
    margin-top: 6px;
    margin-bottom: 12px;
}
