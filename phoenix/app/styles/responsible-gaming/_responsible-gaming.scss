.responsible-gaming {
    .navigation__login-mobile a.navigation__account-mobile,
    header .navigation .login-wrapper {
        display: none;
    }
    .navigation__menu .menu-top-menu-container {
        margin-left: auto;
    }

    &__hero {
        background: $campaign-background;
        position: relative;
        min-height: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-size: cover;
        background-position: center;
        flex-flow: column;

        @include from(md) {
            min-height: 400px;
        }
    }
    &__title {
        @include get-typography('display-3');
        text-align: center;
        color: $color-font-light;
    }
}