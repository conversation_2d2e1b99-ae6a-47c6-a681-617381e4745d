<?php
get_template_part('components/header/phoenix-notification');
get_template_part('components/header/expiry');
get_template_part('components/header/cookie-consent-notification');

echo '<div class="site-header container__header">';

    global $topNotificationElements;
    if (!empty($topNotificationElements)) {
        echo '<div class="notification__container">';
        echo $topNotificationElements;
        echo '</div>';
    }

    global $headerElement;
    if (!empty($headerElement)) {
        echo $headerElement;

    } elseif (empty(get_field('hide_header'))) {
        echo '<header>';

        // Mobile nav with burger style used for non-promo instances
        if(SITE_TYPE != SITE_TYPE_PROMO) {
            get_template_part('components/header/mobile-navigation-burger');
        }

        // Desktop nav commonly used for all instances
        get_template_part('components/header/navigation');

        // Mobile nav with tab style used for promo instances only
        if(SITE_TYPE == SITE_TYPE_PROMO) {
            get_template_part('components/header/mobile-navigation-tabs');
        }

        echo '<div class="navigation__hide-on-scroll">';
            if(is_blog() && (!is_404())) {
                get_template_part('blog/components/categories-nav');
            }

            if (is_single() && get_post_type() == SEO_PAGE_SLUG) {
                get_template_part('seo-page/components/secondary-navigation');
            }

            if(is_page()) {
                get_template_part('components/jump-links', null, [
                    'strip_allow' => '<h2>',
                    'regex' => '#<h2(.*?)>(.*?)<\/h2>#',
                ]);
            } else {
                get_template_part('components/jump-links');
            }

        echo '</div>';
        echo '</header>';
    }

echo '</div>';

get_template_part('components/header/tabs');
