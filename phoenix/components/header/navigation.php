<?php
$navigationContainerClass = getClass([
    "navigation__container",
    [
        'condition' => SITE_TYPE == SITE_TYPE_PROMO,
        'name' => 'navigation__container--promo',
    ]
]);
?>

<div class="navigation">
    <div class="<?= $navigationContainerClass; ?>">
        <?php if (SITE_TYPE != SITE_TYPE_PROMO) : ?>
            <div class="navigation__menu-mobile">
                <?php if (has_nav_menu('mobile-menu')) : ?>
                    <a href="#" class="navigation__menu-mobile" onClick="showMobileNavigation();" role="button"><?= vector('menu'); ?></a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        <div class="navigation__logo">
            <?php get_template_part('components/header/parts/navigation-logo'); ?>
        </div>
        <div class="navigation__menu">
            <?php
            $locations = get_nav_menu_locations();
            $desktopMenuName = 'menu-1'; // old

            /*
                Commented out the codes below,
                since syncing desktop menu was out of scope
                for https://comeon.atlassian.net/browse/PHNXC-884
            */
            // $desktopMenuName = 'desktop-menu_' . CURRENT_REGION; // In case we wanna sync desktop menu in the future

            // if(empty($locations[$desktopMenuName])) {
            //     $desktopMenuName = 'menu-1'; // old
            // }

            if (!empty($locations[$desktopMenuName])) {
                wp_nav_menu([
                    'theme_location' => $desktopMenuName,
                    'walker'   => new Phnx_Walker_Nav_Menu(),
                ]);
            }
            ?>
            <div class="login-wrapper">
                <?php get_template_part('components/header/parts/navigation-cta'); ?>
            </div>
        </div>
        <div class="navigation__login-mobile">
            <?php get_template_part('components/header/parts/navigation-cta-mobile'); ?>
        </div>
        <?php global $HAS_COMPLIANCE;
        if (!$HAS_COMPLIANCE && CURRENT_REGION == 'de') : ?>
            <div class="navigation__plus-18">
                <i class="compliance-header__plus-18">18+</i>
            </div>
        <?php endif; ?>
    </div>
</div>