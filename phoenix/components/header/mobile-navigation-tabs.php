<?php
if (empty($args['location'])) {
    $args['location'] = 'mobile-menu_' . CURRENT_REGION;
}

if (has_nav_menu($args['location'])) : ?>
    <div class="navigation navigation--mobile-tabs">
        <div class="navigation__menu navigation__menu--mobile-tabs">
            <?php wp_nav_menu([
                'theme_location' => $args['location'],
                'walker'   => new Phnx_Walker_Nav_Menu(),
                'sort_column' => 'menu_order',
            ]); ?>
        </div>
    </div>
<?php endif;