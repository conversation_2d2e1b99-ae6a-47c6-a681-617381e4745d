<?php
// Compliance Header - GERMAN GAMING AUTHORITY (GGA)

global $HAS_COMPLIANCE; ?>
<?php if ($HAS_COMPLIANCE) : ?>
    <div class="compliance-header compliance-header--gga">
        <div class="container compliance-header--gga__wrapper">
            <div class="compliance-header--gga__element compliance-header--gga__element--first">
                <?php /* <i class="compliance-header--gga__icon"><?= vector('dice'); ?></i> */?>
            </div>

            <div class="compliance-header--gga__element compliance-header--gga__element--full-width">
                <div class="panic-btn__background js-panic-btn-background">
                    <div class="panic-btn js-panic-btn">
                        <div class="panic-btn__dragable js-panic-btn-dragable">
                            <?= vector('lock-closed'); ?>
                        </div>
                    </div>
                    <div class="panic-btn__label">24h Spielpause</div>
                </div>
            </div>

            <?php /* <div class="compliance-header--gga__element compliance-header--gga__element--timer">
                <div class=" compliance-header--gga__timer">
                    <i class="compliance-header--gga__icon"><?= vector('stopwatch'); ?></i>
                    <div class="js-session-timer-minutes">...</div>
                </div>
            </div> */ ?>

            <div class="compliance-header--gga__element">
                <i class="compliance-header__plus-18">18+</i>
            </div>
        </div>
    </div>

    <?php ob_start();?>
<div class="panic-reason">
    <form action="javascript:void(0);" onSubmit="sendPanicReason(event)">
        <i class="panic-reason__icon"><?= vector('pause'); ?></i>
        <h1 class="panic-reason__title"><?= get_field_tweaked('compliance_access_blocked', 'option'); ?></h1>
        <div class="panic-reason__description"><?= get_field_tweaked('compliance_block_for_24_hours', 'option'); ?></div>
        <h2 class="panic-reason__options-title"><?= get_field_tweaked('compliance_select_a_reason', 'option'); ?></h2>
        <ul class="panic-reason__options">
            <?php for ($i=1; $i <= 4; $i++) : ?>
            <li class="panic-reason__options-item">
                <?php get_template_part('components/form/radio', null, [
                    'label' => get_field_tweaked("compliance_reason_" . $i , 'option'),
                    'class' => 'panic-reason__checkbox',
                    'name' => 'panic_reason',
                    'value' => "MOBILE_PANIC_LOGOUT_REASON_$i",
                    'reverse' => true,
                ]); ?>
            </li>
            <?php endfor; ?>
        </ul>
        <div class="panic-reason__btn">
            <button type="submit" class="btn btn--primary btn--rounded js-panic-reason-btn"><?= get_field_tweaked('compliance_close', 'option'); ?></button>
        </div>
    </form>
</div>
    <?php
    $content = ob_get_clean();

    get_template_part('components/modal', 'simple', [
        'slug'      => 'panic-reason',
        'class'     => 'modal--panic-reason',
        'headline'  => false,
        'content'   => $content
    ]);
    ?>
<?php endif; ?>
<?php
$notificationArgs = [
	'message' => get_field_tweaked('compliance_user_locked_out', 'option'),
	'type' => 'info',
	'id' => 'alert_user_locked_out'
];
get_template_part('components/notification', null, $notificationArgs);
?>