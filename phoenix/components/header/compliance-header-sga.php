<?php 
// Compliance Header - SWEDISH GAMING AUTHORITY (SGA)

global $HAS_COMPLIANCE; ?>
<?php if ($HAS_COMPLIANCE) : ?>
    <div class="compliance-header compliance-header--sga">
        <div class="compliance-header--sga__wrapper">
            <div class="compliance-header--sga__element compliance-header--sga__element--spelpaus">
                <?php if (player()->isLoggedIn()) : ?>
                    <a href="<?= brandUrl(); ?>/sv/sportsbook?sidebar=account/settings/close-catalog">
                <?php else : ?>
                    <a href="https://www.spelpaus.se/?scos=true" target="_blank" title="Spelpaus">
                <?php endif; ?>
                        <?php 
                            echo vector('spelpaus-colored', 'logos');
                        ?>
                    </a>
            </div>
            <div class="compliance-header--sga__element compliance-header--sga__element--spelgranser">
                <a href="<?= brandUrl(); ?>/account/my-limits" title="Spelgränser">
                    <?php 
                        echo vector('spelgranser-colored', 'logos');
                    ?>
                </a>
            </div>
            <div class="compliance-header--sga__element sjalvtest">
                <a href="https://stodlinjen.se/#!/spelberoende-test-pgsi" target="_blank" title="Stödlinjen">
                    <?php 
                        echo vector('sjalvtest-colored', 'logos');
                    ?>
                </a>
            </div>
            <?php
                $plusClass = getClass([
                    'compliance-header--sga__element',
                    'compliance-header--sga__element--plus',
                    [
                        'condition' => player()->isLoggedIn(),
                        'name' => 'compliance-header--sga__element--has-timer',
                    ],
                ]);
            ?>
            <div class="<?= $plusClass ?>">
                <?php if (player()->isLoggedIn()) : ?>
                    <div class="js-session-timer-seconds">...</div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif; ?>