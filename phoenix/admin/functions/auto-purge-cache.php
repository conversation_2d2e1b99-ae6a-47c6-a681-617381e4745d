<?php

function check_and_purge_cache() {
    if (!defined('RELEASE_VERSION')) {
        error_log('check_and_purge_cache(): RELEASE_VERSION is not defined, auto-purge-cache.php will not run');
        return;
    }

    $option_name = 'px_release_version';
    $stored_version = get_option($option_name);

    if ($stored_version !== RELEASE_VERSION) {

        /*
            Send headers to disable caching
            Clear headers only if not already sent
        */

        if (!headers_sent()) {
            header("Cache-Control: no-cache, no-store, must-revalidate");
            header("Pragma: no-cache");
            header("Expires: 0");
        }

        // Purge W3 Total Cache
        if (function_exists('w3tc_flush_all')) {
            w3tc_flush_all();
        }

        // Purge Breeze
        if (class_exists('Breeze_Admin')) {
            do_action('breeze_clear_all_cache');
        }

        // Purge Autoptimize
        if (class_exists('autoptimizeCache')) {
            autoptimizeCache::clearall();
        }

        // Update stored version
        update_option($option_name, RELEASE_VERSION);
    }
}

// Hook into WordPress's init action to run the check with highest priority (-1)
add_action('init', 'check_and_purge_cache', -1);