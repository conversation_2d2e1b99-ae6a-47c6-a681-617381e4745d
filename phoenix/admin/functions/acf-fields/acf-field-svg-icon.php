<?php

if(!class_exists('acf_field')) {
	add_action('admin_notices', function () {
			$class          = 'notice notice-error';
			$notice_message = 'acf_field class cannot be extended in acf-field-svg-icon.php. If you are sure ACF plugin is enabled, than an ACF update might caused this.';
			printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), esc_html($notice_message));
		}
		);
	return;
}

class acf_field_svg_icon extends acf_field {

	public function __construct() {
		// vars
		$this->name     = 'svg_icon';
		$this->label    = __('Icon');
		$this->category = __('Phoenix');

		// do not delete!
		parent::__construct();
	}

	public function render_field($field) {
?>
		<input class="widefat" value="<?php echo esc_attr($field['value']); ?>" name="<?php echo esc_attr($field['name']); ?>" data-placeholder="<?php esc_attr_e('Select icon'); ?>" data-allow-clear="true" />
<?php
	}

	public function input_admin_enqueue_scripts() {
		$icons = [];
		foreach (getIcons() as $icon) {
			$icons[] = [
				'id'       => $icon,
				'text'     => $icon
			];
		}

		wp_localize_script('admin-script', 'svg_icon_data', $icons);
	}

	public function input_admin_footer() {
		echo '<?xml version="1.0" encoding="UTF-8"?>';
		echo '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="0" height="0">';

		foreach (getIcons() as $icon) {
			echo '<symbol id="' . $icon . '" width="24" height="24" viewBox="0 0 24 24" fill="none">';
			echo strip_tags((string) vector($icon), '<path>'); // remove <svg> tag
			echo '</symbol>';
		}

		echo '</svg>';
	}
}
