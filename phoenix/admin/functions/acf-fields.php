<?php
// Register ACF fields
add_action('init', 'register_acf_fields');
function register_acf_fields() {
	// Include the corresponding files
    include_once 'acf-fields/acf-field-svg-icon.php';

	if (! class_exists('acf_field_svg_icon')) {
		add_action('admin_notices', function () {
			$class          = 'notice notice-error';
			$notice_message = 'acf_field_svg_icon class does not exist. If you are sure ACF plugin is enabled, than an ACF update might caused this.';
			printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), esc_html($notice_message));
		}
		);
		return;
	}


	new acf_field_svg_icon();

    include_once 'acf-fields/acf-field-block-optin-download-report.php';
	new acf_field_block_optin_download_report();

	if (isFeatureActive('ad-banner')) {
		include_once 'acf-fields/acf-field-block-ad-banner-export.php';
		new acf_field_block_ad_banner_export();
	}
}

include_once 'acf-fields/acf-field-jackpots-providers.php';
