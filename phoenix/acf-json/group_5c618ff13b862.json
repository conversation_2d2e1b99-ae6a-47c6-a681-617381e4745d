{"key": "group_5c618ff13b862", "title": "Setting<PERSON> - <PERSON><PERSON>", "fields": [{"key": "field_5c62d32243fee__trashed", "label": "Show Footer", "name": "show_footer", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "js-show_footer", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Yes", "ui_off_text": "No", "ui": 1}, {"key": "field_65254c0d67e8a", "label": "Synchronized Footer", "name": "sync_footer", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "js-sync_footer", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Yes", "ui_off_text": "No", "ui": 1}, {"key": "field_5cb10515asx82", "label": "SEO", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5c619wq7eabdz", "label": "SEO Text", "name": "description_text", "aria-label": "", "type": "wysiwyg", "instructions": "Text area reserved for SEO that shows right above footer. \r\n\r\nTip: There's also page specific SEO text field on every landing page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-description_text", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 1}, {"key": "field_65f1cb1bba644", "label": "Enable Hidden Keywords", "name": "enable_hidden_keywords", "aria-label": "", "type": "true_false", "instructions": "Please be careful with this as it might cause serious issues", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-show_footer js-show_hidden_keywords js-enable_hidden_keywords", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Yes", "ui_off_text": "No", "ui": 1}, {"key": "field_65f18cfd3d0b6", "label": "Hidden Keywords", "name": "seo_hidden_keywords", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_65f1cb1bba644", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "js-seo_hidden_keywords", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_65f1cbabba645", "label": "Show SEO Links", "name": "show_seo_links", "aria-label": "", "type": "true_false", "instructions": "Shows a list of links at the bottom", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-show_footer js-show_seo_links", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Yes", "ui_off_text": "No", "ui": 1}, {"key": "field_6463448f951b8", "label": "SEO Links", "name": "footer_seo_links", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_65f1cbabba645", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "js-seo_links js-footer_seo_links", "id": ""}, "layout": "block", "pagination": 0, "min": 0, "max": 4, "collapsed": "", "button_label": "Add New Column", "rows_per_page": 20, "sub_fields": [{"key": "field_64634508951bb", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-title", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_6463448f951b8"}, {"key": "field_64634527951bc", "label": "Links", "name": "links", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-links", "id": ""}, "layout": "table", "min": 0, "max": 0, "collapsed": "", "button_label": "Add New Link", "rows_per_page": 20, "parent_repeater": "field_6463448f951b8", "sub_fields": [{"key": "field_64634539951bd", "label": "Link URL", "name": "link_url", "aria-label": "", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-link_url", "id": ""}, "default_value": "", "placeholder": "", "parent_repeater": "field_64634527951bc"}, {"key": "field_64634553951be", "label": "Link Text", "name": "link_text", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-link_text", "id": ""}, "default_value": "", "maxlength": 30, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_64634527951bc"}]}]}, {"key": "field_5c61976968616__trashed", "label": "Languages", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5c6199029c3ef__trashed", "label": "Languages", "name": "footer_languages", "aria-label": "", "type": "select", "instructions": "Mind the order", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-footer_languages", "id": ""}, "choices": {"denmark": "Dansk", "germany": "De<PERSON>ch", "english": "Global", "norway": "Norsk", "poland": "Polska", "eu": "Scandinavian EU", "finland": "<PERSON><PERSON>", "sweden": "Svenska", "uk": "United Kingdom", "nz": "New Zealand"}, "default_value": [], "return_format": "array", "multiple": 1, "allow_null": 0, "ui": 1, "ajax": 0, "placeholder": ""}, {"key": "field_5cb0fd40ddc7e", "label": "Danish URL", "name": "danish_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_5c6199029c3ef__trashed", "operator": "==", "value": "denmark"}]], "wrapper": {"width": "", "class": "js-danish_url", "id": ""}, "default_value": "https://", "placeholder": ""}, {"key": "field_5c619b0a9809f__trashed", "label": "German URL", "name": "germany_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_5c6199029c3ef__trashed", "operator": "==", "value": "germany"}]], "wrapper": {"width": "", "class": "js-germany_url", "id": ""}, "default_value": "https://", "placeholder": ""}, {"key": "field_5c6199369c3f0__trashed", "label": "Global URL", "name": "english_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_5c6199029c3ef__trashed", "operator": "==", "value": "english"}]], "wrapper": {"width": "", "class": "js-english_url", "id": ""}, "default_value": "https://", "placeholder": ""}, {"key": "field_5c619bc6980a0__trashed", "label": "Norwegian URL", "name": "norway_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_5c6199029c3ef__trashed", "operator": "==", "value": "norway"}]], "wrapper": {"width": "", "class": "js-norway_url", "id": ""}, "default_value": "https://", "placeholder": ""}, {"key": "field_5cb0feeb3fb91", "label": "Polish URL", "name": "poland_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_5c6199029c3ef__trashed", "operator": "==", "value": "poland"}]], "wrapper": {"width": "", "class": "js-poland_url", "id": ""}, "default_value": "https://", "placeholder": ""}, {"key": "field_5cb0fd2cddc7d", "label": "Scandinavian EU URL", "name": "eu_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_5c6199029c3ef__trashed", "operator": "==", "value": "eu"}]], "wrapper": {"width": "", "class": "js-eu_url", "id": ""}, "default_value": "https://", "placeholder": ""}, {"key": "field_5cb0ff043fb92", "label": "Finnish URL", "name": "finland_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_5c6199029c3ef__trashed", "operator": "==", "value": "finland"}]], "wrapper": {"width": "", "class": "js-finland_url", "id": ""}, "default_value": "https://", "placeholder": ""}, {"key": "field_5c619aff9809e__trashed", "label": "Swedish URL", "name": "sweden_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_5c6199029c3ef__trashed", "operator": "==", "value": "sweden"}]], "wrapper": {"width": "", "class": "js-sweden_url", "id": ""}, "default_value": "https://", "placeholder": ""}, {"key": "field_5cb0ff153fb93", "label": "UK URL", "name": "uk_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_5c6199029c3ef__trashed", "operator": "==", "value": "uk"}]], "wrapper": {"width": "", "class": "js-uk_url", "id": ""}, "default_value": "https://", "placeholder": ""}, {"key": "field_5c6196edbca8c__trashed", "label": "Navigation Menu", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5c6199d62f3ce__trashed", "label": "Left Area", "name": "footer_left_area", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-footer_left_area", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Link", "rows_per_page": 20, "sub_fields": [{"key": "field_5c6199eb2f3cf", "label": "Item Text", "name": "item_text", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-item_text", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_5c6199d62f3ce__trashed"}, {"key": "field_5c619a052f3d0", "label": "Item URL", "name": "item_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-item_url", "id": ""}, "default_value": "https://", "placeholder": "", "parent_repeater": "field_5c6199d62f3ce__trashed"}]}, {"key": "field_5c619a1f2f3d1__trashed", "label": "Right Area", "name": "footer_right_area", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-footer_right_area", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Link", "rows_per_page": 20, "sub_fields": [{"key": "field_5c619a1f2f3d2", "label": "Item Text", "name": "item_text", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-item_text", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_5c619a1f2f3d1__trashed"}, {"key": "field_5c619a1f2f3d3", "label": "Item URL", "name": "item_url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-item_url", "id": ""}, "default_value": "https://", "placeholder": "", "parent_repeater": "field_5c619a1f2f3d1__trashed"}]}, {"key": "field_5c61978768617__trashed", "label": "Footer Text", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5cb1051160d81", "label": "Description", "name": "footer_description_text", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "100", "class": "js-footer_description_text", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "new_lines": ""}, {"key": "field_6388f4bacad95", "label": "Bottom Compliance Label", "name": "footer_compliance_label", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "100", "class": "js-footer_compliance_label", "id": ""}, "default_value": "", "maxlength": 200, "rows": 2, "placeholder": "", "new_lines": ""}, {"key": "field_5cb1051560d82", "label": "Disclaimer Block", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5cb0e0e42490f", "label": "License Logo", "name": "main_compliance_logo", "aria-label": "", "type": "radio", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "30", "class": "js-main_compliance_logo", "id": ""}, "choices": {"igaming-ontario": "iGaming Ontario", "ggl": "GGL", "gamblingcommission": "Gambling Commission", "mga-license": "Malta Gaming Authority (MGA)", "legalnybukmacher": "<PERSON><PERSON>", "spellinspektionen": "Spelinspektionen", "spillemyndigheden": "S<PERSON><PERSON><PERSON><PERSON><PERSON>eden", "kansspelautoriteit-white": "Kansspelautoriteit (white)", "kansspelautoriteit-black": "Kansspelautoriteit (black)"}, "default_value": "", "return_format": "value", "allow_null": 0, "other_choice": 0, "layout": "vertical", "save_other_choice": 0}, {"key": "field_5c619d57eabdd__trashed", "label": "Disclaimer Text", "name": "footer_disclaimer_text", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "70", "class": "js-footer_disclaimer_text", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "new_lines": ""}, {"key": "field_6228c0f5e9e88", "label": "License Logo Link", "name": "footer_license_logo_link", "aria-label": "", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "100", "class": "js-footer_license_logo_link", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_5cb0dcfa77e85", "label": "Logos", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5c61907252dfd__trashed", "label": "Payment Logos", "name": "footer_payment_logos", "aria-label": "", "type": "select", "instructions": "Mind the order", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33.33", "class": "js-footer_payment_logos", "id": ""}, "choices": {"airtel": "Airtel", "applepay": "Apple Pay", "astropay": "AstroPay", "axis": "AXIS Bank", "bank": "Bank", "bankid": "BankID", "bitcoin": "Bitcoin", "blik": "Blik", "brite": "Brite", "dotpay": "Dotpay", "ecashout": "eCashout", "ecopayz": "ecoPayz", "entropay": "Entropay", "euteller": "<PERSON><PERSON><PERSON>", "ezeewallet": "eZeeWallet", "giropay": "Giropay", "hdfc": "HDFC Bank", "icici": "ICICI Bank", "ideal": "iDeal", "idebit": "iDebit", "interac": "Interac", "instadebit": "Instadebit", "imoje": "imoje", "iwallet": "iWallet", "jcb": "JCB", "jetongo": "Jeton GO", "jio": "<PERSON><PERSON>", "line": "LINE Pay", "luxon": "<PERSON><PERSON>", "maestro": "Maestro", "mastercard": "Master Card", "mifinity": "Mifinity", "mobikwik": "Mobikwik", "mobilepay": "MobilePay", "mojeid": "mojeID", "much-better": "MuchBetter", "netbanking": "Netbanking", "neteller": "<PERSON><PERSON>", "neosurf": "Neosurf", "nordea": "Nordea", "orientalwallet": "Oriental Wallet", "paydo": "<PERSON><PERSON>", "paypal": "PayPal", "paysafe": "Paysafe", "paytm": "Paytm", "phonepe": "PhonePe", "przelewy": "Przelewy 24", "revolut": "Revolut", "sbi": "State Bank of India", "siirto": "Siirto", "siru": "<PERSON><PERSON>", "skrill": "Skrill", "skrill1tap": "Skill 1-tap", "sms-voucher": "SMS Voucher", "sofort": "Sofort", "sumopay": "SumoPay", "swish": "Swish", "tether-usdt": "Tether (USDT)", "tigerpay": "TigerPay", "trustly": "Trustly", "transactionlink": "TransactionLink", "tpay": "Tpay", "upi": "UPI", "venus-point": "Venus Point", "visa": "Visa", "visabyvoucher": "Visa by Voucher", "zimpler": "<PERSON><PERSON><PERSON>"}, "default_value": [], "return_format": "value", "multiple": 1, "allow_null": 1, "ui": 1, "ajax": 1, "placeholder": ""}, {"key": "field_5cb0dcb5058be", "label": "Compliance Logos", "name": "footer_compliance_logos", "aria-label": "", "type": "select", "instructions": "Mind the order", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33.33", "class": "js-footer_compliance_logos", "id": ""}, "choices": {"eu": "EU", "plus-18": "18+", "plus-19": "19+", "begambleaware": "BeGambleAware.org", "dswv": "DSWV", "ibas": "IBAS", "gamcare": "GamCare", "gamstop": "GamStop", "stodlinjen": "St<PERSON>dlinjen", "stopspillet": "StopSpillet"}, "default_value": [], "return_format": "value", "multiple": 1, "allow_null": 1, "ui": 1, "ajax": 1, "placeholder": ""}, {"key": "field_6613ce1003a1a", "label": "Award Logos", "name": "footer_award_logos", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33.33", "class": "js-footer_award_logos", "id": ""}, "message": "Show EGR logos on footer", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}], "location": [[{"param": "options_page", "operator": "==", "value": "acf-options-footer-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 1, "modified": 1744059222}